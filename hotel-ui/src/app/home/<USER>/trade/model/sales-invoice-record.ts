import {MetaData} from '../../../core/model/metaData';
import {Stock} from '../../inventory/model/stock';
import {SubCategory} from '../../inventory/model/sub-category';

export class SalesInvoiceRecord {

  id: string;

  itemCode: string;

  stockId: string; // Reference to the Stock record ID

  barcode: string;

  ticketType: string;

  itemName: string;

  // Subcategory information for reporting and filtering
  subCategory: SubCategory;

  subCategoryName: string; // Denormalized for performance

  quantity: number;

  unitPrice: number;

  // what ever the chane we do to the unit price this will remain unchanged as original selling price.This and itemcode both use to find the stock record`
  unitPriceOriginal: number;

  itemCost: number;

  discount: number;

  price: number;

  date: Date;

  drawerNo: string;

  recordType: string;

  paymentStatus: string;

  stock: Stock;

  displayQuantity: number;

  isManageStock: boolean;

  warehouse: any;

  printed: boolean;

  unprintedQuantity: number; // Tracks quantity added since last print

  isAutoAddedEmptyBottle: boolean; // Flag to indicate this is an auto-added empty bottle (for stock decrease)
}
