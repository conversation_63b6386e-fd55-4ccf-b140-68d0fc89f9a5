import {environment} from '../../../../environments/environment';

export class TradeConstants {

  public static API_URL = environment.apiUrl;

  public static SAVE_PURCHASE_INVOICE = TradeConstants.API_URL + 'purchaseInvoice/save';
  public static GET_PURCHASE_INVOICE_PAGINATION = TradeConstants.API_URL + 'purchaseInvoice/findAllPages';
  public static FIND_ALL_BY_INVOICE_LIKE = TradeConstants.API_URL + 'purchaseInvoice/searchByName';
  public static FIND_ALL_BY_SUPPLIER = TradeConstants.API_URL + 'purchaseInvoice/searchBySupplier';
  public static FIND_ALL_BY_PURCHASE_INVOICE_NO = TradeConstants.API_URL + 'purchaseInvoice/searchByInvoiceNo';
  public static FIND_ALL_PIS_BY_DATE = TradeConstants.API_URL + 'purchaseInvoice/findAllByDate';
  public static FIND_ALL_BY_INVOICE_ID = TradeConstants.API_URL + 'purchaseInvoice/findById';
  public static FIND_ALL_PIS_BY_STATUS_ID = TradeConstants.API_URL + 'purchaseInvoice/findAllByStatusId';

  public static PAY_PI_BALANCE = TradeConstants.API_URL + 'purchaseInvoice/payBalance';
  public static SAVE_DEPOSIT = TradeConstants.API_URL + 'deposit/save';

  public static FIND_BY_CHEQUE = TradeConstants.API_URL + 'deposit/findByCheque';
  public static GET_ALL_PAYMENT_METHOD = TradeConstants.API_URL + 'paymentMethod/findAll';
  public static FIND_PAYMENT_METHOD_BY_ID = TradeConstants.API_URL + 'paymentMethod/findById';

  public static SAVE_QUOTATION = TradeConstants.API_URL + 'quotation/save';
  public static GET_QUOTATION = TradeConstants.API_URL + 'quotation/getAll';
  public static FIND_QUOTATION = TradeConstants.API_URL + 'quotation/findAll';
  public static FIND_ALL_BY_QUOTATION_ID = TradeConstants.API_URL + 'quotation/findAllQId';
  public static FIND_BY_QUOTATION_ID = TradeConstants.API_URL + 'quotation/findQuotationByQId';
  public static FIND_ALL_QUOTATION_BY_CUSTOMER = TradeConstants.API_URL + 'quotation/findAllByCustomer';
  public static DELETE_QUOTATION = TradeConstants.API_URL + 'quotation/delete';
  public static CONVERT_TO_INVOICE = TradeConstants.API_URL + 'quotation/convertToInvoice';

  public static SAVE_SALES_INVOICE = TradeConstants.API_URL + 'salesInvoice/save';
  // Invoice update functionality removed - only amendments are allowed
  public static GET_SALES_INVOICE = TradeConstants.API_URL + 'salesInvoice/findAllPages';
  public static GET_PENDING_SALES_INVOICE = TradeConstants.API_URL + 'salesInvoice/findAllPendingPages';
  public static AMEND_SI = TradeConstants.API_URL + 'salesInvoice/amendSi';
  public static GET_SALES_INVOICE_BY_ID = TradeConstants.API_URL + 'salesInvoice/findById';
  public static GET_SALES_INVOICE_RECORD_BY_ID = TradeConstants.API_URL + 'salesInvoiceRecord/findById';
  public static GET_ALL_INCOMPLETE = TradeConstants.API_URL + 'salesInvoice/findAllIncomplete';
  public static FIND_SI_BY_CUST_NIC_BR = TradeConstants.API_URL + 'salesInvoice/findAllByCustomer';
  public static FIND_SI_BY_CUST_ID = TradeConstants.API_URL + 'salesInvoice/findAllByCustomerId';
  public static FIND_SI_BY_PAYMENT_TYPE = TradeConstants.API_URL + 'salesInvoice/findAllByPaymentType';
  public static FIND_SI_BY_INV_NO = TradeConstants.API_URL + 'salesInvoice/findByInvoiceNo';
  public static FIND_SI_BY_ORDER_NO = TradeConstants.API_URL + 'salesInvoice/findByOrderNo';
  public static FIND_SI_BY_JOB_NO = TradeConstants.API_URL + 'salesInvoice/findByJobNo';
  public static CANCEL_SALES_INVOICE = TradeConstants.API_URL + 'salesInvoice/cancel';

  public static PAY_SI_BALANCE = TradeConstants.API_URL + 'salesInvoice/payBalance';
  public static FIND_ALL_SIS_BY_DATE = TradeConstants.API_URL + 'salesInvoice/findAllByDate';
  public static FIND_WITH_FILTERS = TradeConstants.API_URL + 'salesInvoice/findWithFilters';
  public static FIND_SALES_GROUP_BY_DATE_BETWEEN = TradeConstants.API_URL + 'salesInvoiceRecord/findSalesGroupByDateBetween';
  public static FIND_PROFIT_BETWEEN = TradeConstants.API_URL + 'salesInvoiceRecord/findProfitBetween';
  public static FIND_UNREALIZED_PROFIT_BETWEEN = TradeConstants.API_URL + 'salesInvoiceRecord/findUnrealizedProfitBetween';
  // Removed FIND_UNREALIZED_PROFIT_BY_DATE_RANGE - now using FIND_PROFIT_BETWEEN with unrealized parameter
  public static FIND_SALES_ITEM_GROUP_BY_DATE = TradeConstants.API_URL + 'salesInvoiceRecord/findSalesItemGroupByDate';
  public static FIND_SALES_BY_CASHIER = TradeConstants.API_URL + 'salesInvoiceRecord/findSalesByCashier';
  public static FIND_SALES_BY_USER = TradeConstants.API_URL + 'salesInvoiceRecord/findSalesByUser';
  public static FIND_SALES_BY_ROUTE = TradeConstants.API_URL + 'salesInvoiceRecord/findSalesByRoute';
  public static FIND_SALES_BY_USER_AND_ROUTE = TradeConstants.API_URL + 'salesInvoiceRecord/findSalesByUserAndRoute';

  public static FIND_BY_PAYMENT_METHOD = TradeConstants.API_URL + 'salesInvoice/findAllByPaymentMethod';
  public static FIND_ALL_BY_PAYMENT_STATUS = TradeConstants.API_URL + 'salesInvoice/findAllByPaymentStatus';
  public static FIND_PENDING_SI_BY_CUSTOMER = TradeConstants.API_URL + 'salesInvoice/findPendingByCustomer';
  public static GET_SALES_INVOICE_RECORD_BETWEEN = TradeConstants.API_URL + 'salesInvoiceRecord/findProfitBetween';
  public static GET_SALES_INVOICE_RECORD_BY_RANGE = TradeConstants.API_URL + 'salesInvoiceRecord/findProfitByRangeFilter';
  public static SAVE_CUSTOMER = TradeConstants.API_URL + 'customer/save';
  public static FIND_CUSTOMER_BY_ID = TradeConstants.API_URL + 'customer/findById';
  public static FIND_ALL_CUSTOMERS = TradeConstants.API_URL + 'customer/findAll';
  public static FIND_BY_CUSTOMER_NAME = TradeConstants.API_URL + 'customer/searchByName';

  public static FIND_BY_CUSTOMER_NAME_LIKE = TradeConstants.API_URL + 'customer/searchByNameLike';
  public static FIND_CUSTOMER_BY_NIC_LIKE = TradeConstants.API_URL + 'customer/searchByNicLike';
  public static FIND_CUSTOMER_BY_TP_LIKE = TradeConstants.API_URL + 'customer/searchByTpLike';
  public static CUSTOMER_NIC_CHECK = TradeConstants.API_URL + 'customer/checkNic';
  public static FIND_CUSTOMER_BY_NO = TradeConstants.API_URL + 'customer/findByCustomerNo';
  public static SAVE_SUPPLIER = TradeConstants.API_URL + 'supplier/save';
  public static GET_SUPPLIERS = TradeConstants.API_URL + 'supplier/findAll';

  public static DELETE_SUPPLIER = TradeConstants.API_URL + 'supplier/delete';
  public static GET_LAST_SUPPLIER = TradeConstants.API_URL + 'supplier/getLastSupplier';
  public static SEARCH_SUPPLIER_BY_NAME_LIKE = TradeConstants.API_URL + 'supplier/searchByName';

  public static GET_SUPPLIER_BY_ID = TradeConstants.API_URL + 'supplier/searchById';
  public static SAVE_EXPENSE_TYPE = TradeConstants.API_URL + 'expenseType/save';
  public static GET_EXPENSE_TYPE = TradeConstants.API_URL + 'expenseType/findAllPage';
  public static SEARCH_EXPENSE_TYPE = TradeConstants.API_URL + 'expenseType/findByName';
  public static SAVE_EXPENSE = TradeConstants.API_URL + 'expense/save';
  public static SAVE_CHEQUE = TradeConstants.API_URL + 'cheque/save';
  public static FIND_ALL_PENDING_CHEQUES = TradeConstants.API_URL + 'cheque/findAllPending';
  public static FIND_ALL_CHEQUE_BY_STATUS = TradeConstants.API_URL + 'cheque/findAllByStatus';
  public static FIND_ALL_CHEQUE_BY_BANK = TradeConstants.API_URL + 'cheque/findAllByBank';

  public static FIND_ALL_CHEQUE_BY_CUSTOMER = TradeConstants.API_URL + 'cheque/findAllByCustomer';
  public static FIND_ALL_CHEQUE_BY_SUPPLIER = TradeConstants.API_URL + 'cheque/findAllBySupplier';
  public static FIND_ALL_CHEQUE_BY_TYPE = TradeConstants.API_URL + 'cheque/findAllByChequeType';
  public static FIND_ALL_CHEQUE_BY_PI_NO = TradeConstants.API_URL + 'cheque/findAllByPurchaseInvoiceNo';
  public static UPDATE_CHEQUE = TradeConstants.API_URL + 'cheque/updateCheque';

  public static FIND_ALL_CHEQUE = TradeConstants.API_URL + 'cheque/findAllPageable';
  public static LOAD_AVAILABLE_CHEQUE_QTY = TradeConstants.API_URL + 'cheque/loadAvailableChequeQty';

  public static FIND_CASH_DRAWER_BY_DRAWER_NO = TradeConstants.API_URL + 'cashDrawer/findByDrawerNo';
  public static CHECK_CASH_DRAWER_NO_STATUS = TradeConstants.API_URL + 'cashDrawer/checkDrawerNoStatus';
  public static FIND_ALL_CASH_DRAWERS = TradeConstants.API_URL + 'cashDrawer/findAllCashDrawers';
  public static DAY_START = TradeConstants.API_URL + 'cashRecord/dayStart';
  public static ADD_CASH = TradeConstants.API_URL + 'cashRecord/addCash'
  public static WITHDRAW_CASH =  TradeConstants.API_URL + 'cashRecord/withdrawCash';

  public static FIND_BY_DRAWER_NO_AND_DATES_BETWEEN =  TradeConstants.API_URL + 'cashRecord/findByDrawerNoAndDates';
  public static FIND_BY_TYPE_AND_DATES_BETWEEN =  TradeConstants.API_URL + 'cashRecord/findByTypeAndDates';
  public static FIND_BY_DRAWER_NO_AND_TYPE_AND_DATES_BETWEEN =  TradeConstants.API_URL + 'cashRecord/findByDrawerNoAndTypeAndDates';
  public static FIND_BY_DATES_BETWEEN =  TradeConstants.API_URL + 'cashRecord/findByDates';
  public static SAVE_CASH_DRAWER_HISTORY = TradeConstants.API_URL + 'cashDrawerHistory/dayClose'

}
