<div class="container-fluid mt-3" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <!-- <PERSON><PERSON> Close Button (only for modal mode) -->
  <div *ngIf="isModal" class="d-flex justify-content-end mb-3">
    <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>

  <div class="row g-4">
    <!-- Main Section -->
    <div class="col-12">
      <div class="card shadow-sm">
        <h3 class="mb-3 text-primary fw-bold">Payment History</h3>
          </div>
        </div>
    <div class="row mt-2">
      <div class="col-md-12">
        <label class="fw-bold">Invoice Created Date : </label>
        <label class="ms-3">{{invoice.date | date}}</label>
      </div>
      <table class="table table-hover table-striped">
        <thead align="center">
        <tr>
          <th scope="col">Date</th>
          <th scope="col">Invoice No</th>
          <th scope="col">Amount</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let tr of transactions,let i = index" align="center">
          <td>{{tr.date | date:'short': '+530'}}</td>
          <td>{{tr.refNo}}</td>
          <td>{{tr.amount | number : '1.2-2'}}</td>
        </tr>
        </tbody>
      </table>
          </div>
</div>

