import { Component, OnInit } from '@angular/core';
import {MetaData} from "../../../../core/model/metaData";
import {BsModalRef} from "ngx-bootstrap/modal";

@Component({
standalone: false,
  selector: 'app-pi-payment-method',
  templateUrl: './pi-payment-method.component.html',
  styleUrls: ['./pi-payment-method.component.css']
})
export class PiPaymentMethodComponent implements OnInit {

  modalRef : BsModalRef;
  piPaymentMethods : Array<MetaData>;
  paymentMethod: MetaData;
  selectedRow: number;

  constructor(  ) { }

  ngOnInit(): void {
  }

  setCashierPayment(paymentMethod, i) {
    this.selectedRow = i
    this.paymentMethod = paymentMethod;
    this.modalRef.hide();
  }
}
