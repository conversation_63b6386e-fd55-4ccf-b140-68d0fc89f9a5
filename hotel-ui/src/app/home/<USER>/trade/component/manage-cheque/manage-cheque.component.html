<div class="container-fluid px-0"><h5 class="card-title mb-0">Manage Cheque</h5>
  <!-- Cheque Type Filter -->
  <div class="row mb-3">
    <div class="col-md-12">
      <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
        <label class="btn btn-primary btn-outline btn-primary" [class.active]="chequeType === 'ALL'">
          <input type="radio" name="chequeTypeFilter" id="all" [value]="'ALL'"
                 [(ngModel)]="chequeType" (change)="filterByChequeType('ALL')"> All Cheques
        </label>
        <label class="btn btn-primary btn-outline btn-primary" [class.active]="chequeType === 'RECEIVED'">
          <input type="radio" name="chequeTypeFilter" id="received" [value]="'RECEIVED'"
                 [(ngModel)]="chequeType" (change)="filterByChequeType('RECEIVED')"> Received Cheques (from Customers)
        </label>
        <label class="btn btn-primary btn-outline btn-primary" [class.active]="chequeType === 'GIVEN'">
          <input type="radio" name="chequeTypeFilter" id="given" [value]="'GIVEN'"
                 [(ngModel)]="chequeType" (change)="filterByChequeType('GIVEN')"> Given Cheques (to Suppliers)
        </label>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Customer search - only shown when viewing ALL or RECEIVED cheques -->
    <div class="col-md-4" *ngIf="chequeType === 'ALL' || chequeType === 'RECEIVED'">
      <div class="input-group search-professional">
        <input type="text" class="form-control"
               [(ngModel)]="keyCustomer"
               [typeahead]="customerList"
               (typeaheadLoading)="loadCustomer()"
               (typeaheadOnSelect)="setSelectedCustomer($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadOptionField="name"
               size="16"
               autocomplete="off"
               placeholder="Search Customer name">
        <button class="btn btn-primary" type="button">
          <i class="fas fa-search"></i>
        </button>
      </div>
    </div>

    <!-- Supplier search - only shown when viewing ALL or GIVEN cheques -->
    <div class="col-md-4" *ngIf="chequeType === 'ALL' || chequeType === 'GIVEN'">
      <div class="input-group search-professional">
        <input type="text" class="form-control"
               [(ngModel)]="keySupplier"
               [typeahead]="supplierList"
               (typeaheadLoading)="loadSupplier()"
               (typeaheadOnSelect)="setSelectedSupplier($event)"
               [typeaheadOptionsLimit]="15"

               typeaheadOptionField="name"
               size="16"
               autocomplete="off"
               placeholder="Search Supplier name">
          <button class="btn btn-primary" type="button">
            <i class="fas fa-search"></i>
          </button>
      </div>
    </div>

    <div class="col-md-4">
      <select name="chequeStatus" class="form-control"
              style="min-height: 38px; line-height: 1.5; padding: 0.5rem 0.75rem;" (change)="findByChequeStatus()"
              #chequeStatus="ngModel" [(ngModel)]="chequeStatusId">
        <option [value]="undefined" disabled>Search Status</option>
        <option *ngFor="let status of statusList" [value]="status.id">
          {{ status.value }}
        </option>
      </select>
    </div>
    <div class="col-md-4">
      <select name="chequeStatus" class="form-control"
              style="min-height: 38px; line-height: 1.5; padding: 0.5rem 0.75rem;" (change)="findByBank()"
              #bank="ngModel" [(ngModel)]="bankId">
        <option [value]="undefined" disabled>Search Bank</option>
        <option *ngFor="let bank of bankList" [value]="bank.id">
          {{ bank.value }}
        </option>
      </select>
    </div>
  </div>
  <div class="row mt-2">
    <table >
      <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Cheque No</th>
        <th>Invoice No</th>
        <th>Bank Name</th>
        <th>Cheque Date</th>
        <th>Amount</th>
        <th>Status</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let cheque of chequeList, let i = index" (click)="selectCheque(cheque, i)"
          [class.active]="i === selectedRow" align="center">
        <td>
          <span *ngIf="cheque.chequeType === 'RECEIVED'" class="badge bg-success">Received</span>
          <span *ngIf="cheque.chequeType === 'GIVEN'" class="badge bg-warning">Given</span>
        </td>
        <td>
          <span *ngIf="cheque.chequeType === 'RECEIVED'">{{ cheque.customer?.name || 'N/A' }}</span>
          <span *ngIf="cheque.chequeType === 'GIVEN'">{{ cheque.supplier?.name || 'N/A' }}</span>
        </td>
        <td>{{ cheque.chequeNo }}</td>
        <td>
          <span *ngIf="cheque.chequeType === 'RECEIVED'">{{ cheque.invoiceNo || 'N/A' }}</span>
          <span *ngIf="cheque.chequeType === 'GIVEN'">{{ cheque.purchaseInvoiceNo || 'N/A' }}</span>
        </td>
        <td>{{ cheque.bank.value }}</td>
        <td>{{ cheque.chequeDate | date: 'shortDate' }}</td>
        <td>{{ cheque.chequeAmount != undefined ? cheque.chequeAmount : 'N/A' }}</td>
        <td>{{ cheque.status.value }}</td>
      </tr>
      </tbody>
    </table>
    <div class="row">
      <div class="col-md-12">
        <pagination
          class="pagination-sm pagination pagination-sm justify-content-center justify-content-center pagination-professional"
          [totalItems]="collectionSize"
          [maxSize]="maxSize"
          [boundaryLinks]="true"
          [(ngModel)]="page"
          (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12 text-end">
      <button type="button" class="btn btn-danger ms-2" [disabled]="!isPending"
              (click)="returned(deposit)">Returned
      </button>
      <button type="button" class="btn btn-danger ms-2" [disabled]="!isPending"
              (click)="Deposit(deposit)">Deposited
      </button>
    </div>
  </div>
</div>

<ng-template #deposit>
  <div class="modal-header">
    <h4 class="modal-title pull-left" *ngIf="isDeposit">Deposit Cheque</h4>
    <h4 class="modal-title pull-left" *ngIf="!isDeposit">Return Cheque</h4>
    <button *ngIf="isModal" type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
  </div>
  <div class="modal-body m-2">
    <div class="row">
      <input type="text" placeholder="Enter Comment" [(ngModel)]="selectedCheque.comment" class="form-control">
    </div>
    <div class="row">
      <div class="col-md-12 text-end">
        <button class="btn btn-danger pull-right mt-3" (click)="saveDeposit(isDeposit)">
          Confirm and Save
        </button>
      </div>
    </div>
  </div>
</ng-template>





