<div class="container-fluid px-0 p-2">
  <div class="theme-color">
    <div *ngIf="!modalRef" class="d-flex justify-content-between align-items-center header-section">
      <div>
        <h2 class="mb-0">{{ 'SITE_NAME' | translate }}</h2>
      </div>
      <div class="d-none d-md-block">
        <i class="fa fa-person fa-2x ms-3 select-item cursor-pointer"
           (click)="openCustomer()"></i>
        <i class="fa fa-luggage-cart fa-2x ms-3 select-item cursor-pointer"
           (click)="openStock()"></i>
        <i class="fa fa-history fa-2x ms-3 select-item cursor-pointer"
           (click)="openPastInvoice()"></i>
        <i class="fa fa-money-bill fa-2x ms-3 select-item cursor-pointer"
           (click)="openCashier()"></i>
        <i class="fa fa-home fa-2x ms-3 select-item cursor-pointer"
           routerLink="../home/<USER>" (click)="closeFullscreen()"></i>
      </div>
    </div>
    <div *ngIf="modalRef" class="d-flex justify-content-between align-items-center header-section">
      <div>
        <h2 class="mb-0">Create Invoice</h2>
      </div>
      <div>
        <button type="button" class="btn btn-sm btn-primary-outline-secondary" (click)="close()">
          <i class="fa fa-times"></i>
        </button>
      </div>
    </div>
    <!-- Mobile action buttons -->
    <div class="d-flex justify-content-between mt-2 d-md-none">
      <button class="btn btn-sm btn-secondary px-2" (click)="openCustomer()">
        <i class="fa fa-person"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" (click)="openStock()">
        <i class="fa fa-luggage-cart"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" (click)="openPastInvoice()">
        <i class="fa fa-history"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" (click)="openCashier()">
        <i class="fa fa-money-bill"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" routerLink="../home/<USER>" (click)="closeFullscreen()">
        <i class="fa fa-home"></i>
      </button>
    </div>
  </div>
  <div class="content-section overflow-auto" (keydown.delete)="clear()">
    <div class="p-0 m-0">
      <!-- Item search section - responsive layout -->
      <div class="row mt-0 mx-0">
        <!-- Barcode field -->
        <div class="mb-3 col-md-2 col-12 mb-2 px-1">
          <label>{{ 'INVENTORY.BARCODE' | translate }}</label>
          <div class="input-group">
            <!-- Original typeahead barcode input - commented out -->
            <!--
            <input [(ngModel)]="keyItemSearch"
                   [typeahead]="itemSearchList"
                   (typeaheadLoading)="searchItems()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="barcode"
                   autocomplete="off"
                   #barcodeEle (keydown.enter)="handleBarcodeEnter($event)"
                   class="form-control" name="searchItem">
            -->
            <!-- New simple barcode input -->
            <input [(ngModel)]="keyItemSearch"
                   autocomplete="off"
                   #barcodeEle (keydown.enter)="handleBarcodeEnterNew($event)"
                   class="form-control" name="searchItem"
                   placeholder="Scan or Search Barcode">
            <button class="btn btn-primary" type="button" (click)="openBarcodeScanner()">
              <i class="fa fa-barcode"></i>
            </button>
          </div>
        </div>

        <!-- Item name field -->
        <div class="mb-3 col-md-5 col-12 mb-2 px-1">
          <label>Item Name</label>
          <input [(ngModel)]="keyItemNameSearch"
                 [typeahead]="itemNameSearchList"
                 (typeaheadLoading)="searchItemsByName()"
                 (typeaheadOnSelect)="setSelectedItem($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadOptionField="itemName"
                 autocomplete="off"
                 class="form-control" name="searchItem">
        </div>

        <!-- Price field -->
        <div class="mb-3 col-md-2 col-4 mb-2 px-1">
          <div class="d-flex justify-content-between align-items-center">
            <label>Price</label>
            <label class="small">{{ "(" + availableQty + ")" }}</label>
          </div>
          <input type="number" required #price="ngModel" #sellingPrice class="form-control" id="price"
                 name="price" [class.is-invalid]="price.invalid && price.touched"
                 [(ngModel)]="sPrice" (keydown.arrowRight)="focusDiscount()">
        </div>

        <!-- Discount field -->
        <div class="mb-3 col-md-1 col-4 mb-2 px-1">
          <label class="d-flex align-items-center">
            Disc
            <span class="ms-1" *ngIf="isPercentage">(<i class="fa fa-percent"
                                                        (click)="isPercentage = !isPercentage"></i>)</span>
            <span class="ms-1" *ngIf="!isPercentage">(<i class="fa fa-minus" (click)="isPercentage = !isPercentage"></i>)</span>
          </label>
          <div class="position-relative">
            <input type="number" #discountForItm="ngModel" #discountForItem class="form-control"
                   id="discount" name="price"
                   [(ngModel)]="discount" (keydown.arrowRight)="focusQty()">
            <span *ngIf="discount && sPrice" class="position-absolute text-secondary"
                  style="right: 10px; top: 50%; transform: translateY(-50%); font-size: 0.85rem;">
              {{ isPercentage ? (sPrice - (sPrice * discount / 100)).toFixed(2) : (sPrice - discount).toFixed(2) }}
            </span>
          </div>
        </div>

        <!-- Quantity field -->
        <div class="mb-3 col-md-2 col-4 mb-2 px-1">
          <div class="d-flex justify-content-between align-items-center">
            <label>Qty</label>
            <div class="form-check form-check-inline m-0 p-0">
              <input class="form-check-input" type="checkbox" [checked]="isReturn" (change)="isReturn = !isReturn">
              <label class="form-check-label small">Return</label>
            </div>
          </div>
          <input type="number" required #qty="ngModel" #quantity class="form-control" id="qty"
                 name="qty" [class.is-invalid]="qty.invalid && qty.touched"
                 [(ngModel)]="itemQty" (keydown.enter)="checkAvailability()"
                 (keydown.arrowLeft)="focusPrice()">
        </div>
        <!-- Full width Add button for mobile only -->
        <button class="btn btn-primary w-100 mt-2 mb-2 d-md-none add-item-btn" type="button"
                (click)="checkAvailability()">
          <i class="fa fa-plus-circle me-2"></i> Add Item
        </button>
      </div>
      <div class="row p-0 m-0">
        <div class="col-md-12 p-0 m-0">
          <div class="table-height">
            <!-- Desktop table view -->
            <div class="table-responsive d-none d-md-block">
              <table class="table table-bordered">
                <thead>
                <tr>
                  <td style="width: 350px; !important;">Barcode</td>
                  <td>Item</td>
                  <td style="width: 150px; !important;">Qty</td>
                  <td style="width: 250px; !important;">Price</td>
                  <td style="width: 12px; !important;"></td>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let siRec of si.salesInvoiceRecords,let i = index"
                    (click)="selectRow(i)" [class.active]="i === selectedSiRecordIndex">
                  <td>{{ siRec.barcode }}</td>
                  <td>{{ siRec.itemName }}</td>
                  <td>{{ siRec.quantity }}</td>
                  <td>{{ siRec.price | number }}</td>
                  <td>
                    <button class="btn btn-danger btn-sm" (click)="removeRow(i)">X</button>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>

            <!-- Mobile card view -->
            <div class="d-md-none">
              <div class="card mb-2" *ngFor="let siRec of si.salesInvoiceRecords,let i = index"
                   (click)="selectRow(i)" [ngClass]="{'border-primary': i === selectedSiRecordIndex}">
                <div class="card-body p-2">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="mb-1">{{ siRec.itemName }}</h6>
                      <small class="text-muted">{{ siRec.barcode }}</small>
                    </div>
                    <div class="text-end">
                      <div><strong>{{ siRec.price | number }}</strong></div>
                      <div>Qty: {{ siRec.quantity }}</div>
                    </div>
                  </div>
                  <div class="text-end mt-2">
                    <button class="btn btn-danger btn-sm" (click)="removeRow(i)">Remove</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment info section - improved for mobile -->
      <div class="row mt-3 mx-0">
        <!-- Customer section -->
        <div class="col-12 col-md-3 mb-2 px-1">
          <label>Customer</label>
          <div class="input-group search-professional">
            <input [(ngModel)]="keyCustomerSearch"
                   [typeahead]="customerSearchList"
                   (typeaheadLoading)="searchCustomers()"
                   (typeaheadOnSelect)="setSelectedCustomer($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadOptionField="name"
                   autocomplete="off"
                   placeholder="Search customer"
                   class="form-control" name="searchCustomer">
            <button class="btn btn-primary input-group-btn btn-primary btn-primary" (click)="newCustomer()"
                    type="button">
              <i class="fa fa-plus"></i>
            </button>
          </div>
        </div>

        <!-- Totals section -->
        <div class="col-12 col-md-9 px-1">
          <div class="row mx-0">
            <div class="col-4 mb-2 px-1">
              <label>Sub Total</label>
              <input class="form-control" [ngModel]="si.subTotal | number" readonly>
            </div>
            <div class="col-4 mb-2 px-1">
              <label>Discount</label>
              <input class="form-control" [(ngModel)]="si.totalDiscount" (ngModelChange)="calculateTotal()">
            </div>
            <div class="col-4 mb-2 px-1">
              <label>Total</label>
              <input class="form-control" [ngModel]="si.totalAmount | number" readonly>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment details section - improved for mobile -->
      <div class="row mt-2 mx-0">
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Reference</label>
          <input type="text" class="form-control" [(ngModel)]="si.reference" placeholder="Reference">
        </div>
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Payment</label>
          <input type="number" class="form-control" [(ngModel)]="si.payment" placeholder="Amount paying" #payment
                 (ngModelChange)="calculateBalance()" (keyup.enter)="saveByEnter()">
        </div>
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Payment Method</label>
          <select class="form-control form-select-fixed form-select"
                  (change)="setPaymentMethod($event)" name="paymentMethodSelected"
                  [(ngModel)]="paymentMethodId" required #paymentMethodSelect="ngModel"
                  [class.is-invalid]="paymentMethodSelect.invalid && paymentMethodSelect.touched">
            <option>-Select-</option>
            <option *ngFor="let method of paymentMethods, let i = index"
                    [value]="method.id">
              {{ method.value }}
            </option>
          </select>
        </div>
        <div class="col-6 col-md-3 mb-2 px-1">
          <label>Balance</label>
          <input class="form-control" [ngModel]="si.cashBalance | number" readonly>
        </div>
      </div>
    </div>
    <!-- Action buttons - optimized for mobile -->
    <div class="mt-3">
      <!-- Desktop view buttons -->
      <div class="d-none d-md-flex justify-content-between align-items-center">
        <!-- Left aligned buttons -->
        <div>
          <button type="button" class="btn btn-primary me-2" [disabled]="!isProcessing"
                  (click)="isProcessing = !isProcessing" title="Reset Controls">
            <i class="fa fa-sync-alt"></i> Reset Controls
          </button>
          <button type="button" class="btn btn-dark"
                  (click)="loadFromMemory()" title="Load Memory">
            <i class="fa fa-memory"></i> Load Memory
          </button>
        </div>

        <!-- Right aligned buttons -->
        <div>
          <button *ngIf="null != si.cheque" class="btn btn-primary btn-lg me-2" mwlConfirmationPopover
                  (confirm)="removeCheque()" [popoverMessage]="'Do you want to remove the Cheque ?'"
                  title="Remove Cheque">
            <i
              class="fa fa-money-check"></i> {{ 'Cheque - ' + si.cheque.chequeNo + ' - ' + si.cheque.chequeAmount + '  X' }}
          </button>
          <button type="button" class="btn btn-danger btn-lg me-2" mwlConfirmationPopover (confirm)="clear()"
                  title="Clear">
            <i class="fa fa-trash"></i> Clear
          </button>
          <!-- Save and Save & Print buttons -->
          <button type="button" class="btn btn-primary btn-lg me-2" (click)="save(false)" [disabled]="isProcessing"
                  title="Save">
            <i class="fa fa-save"></i> Save
          </button>
          <button type="button" class="btn btn-primary btn-lg" (click)="save(true)" [disabled]="isProcessing"
                  title="Save & Print">
            <i class="fa fa-print"></i> Save & Print
          </button>
        </div>
      </div>

      <!-- Mobile view buttons - compact layout -->
      <div class="d-flex d-md-none justify-content-between flex-wrap gap-1">
        <div>
          <button type="button" class="btn btn-primary btn-sm mx-1" [disabled]="!isProcessing"
                  (click)="isProcessing = !isProcessing" title="Reset Controls">
            <i class="fa fa-sync-alt"></i>
          </button>
          <button type="button" class="btn btn-dark btn-sm mx-1"
                  (click)="loadFromMemory()" title="Load Memory">
            <i class="fa fa-memory"></i>
          </button>
          <button *ngIf="null != si.cheque" class="btn btn-primary btn-sm mx-1" mwlConfirmationPopover
                  (confirm)="removeCheque()" [popoverMessage]="'Do you want to remove the Cheque ?'"
                  title="Remove Cheque">
            <i class="fa fa-money-check"></i>
          </button>
        </div>
        <div>
          <button type="button" class="btn btn-danger btn-sm mx-1" mwlConfirmationPopover (confirm)="clear()"
                  title="Clear">
            <i class="fa fa-trash"></i>
          </button>
          <!-- Save and Save & Print buttons -->
          <button type="button" class="btn btn-primary btn-sm mx-1" (click)="save(false)" [disabled]="isProcessing"
                  title="Save">
            <i class="fa fa-save"></i>
          </button>
          <button type="button" class="btn btn-primary btn-sm mx-1" (click)="save(true)" [disabled]="isProcessing"
                  title="Save & Print">
            <i class="fa fa-print"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #tempMultiplePrice>
  <div class="modal-header">
    <h5 class="modal-title">Select Price</h5>
    <button type="button" class="btn-close" aria-label="Close" (click)="hidePricesModal()"></button>
  </div>
  <div class="modal-body text-center">
    <div class="mb-3 col-md-12">
      <ul class="list-group">
        <li *ngFor="let pr of prices; let i = index" class="list-group-item list-group-item-action"
            (click)="setPrice(pr[1], pr[0])">
          {{ pr[1] + " - " + pr[0] }}
        </li>
      </ul>
    </div>
  </div>
</ng-template>

<!-- Empty Bottle Confirmation Modal -->
<ng-template #emptyBottleConfirmationModal>
  <div class="modal-header">
    <h5 class="modal-title">
      <i class="fas fa-wine-bottle me-2 text-primary"></i>
      Add Empty Bottle
    </h5>
  </div>
  <div class="modal-body text-center p-4">
    <div class="mb-3">
      <i class="fas fa-question-circle fa-3x text-primary mb-3"></i>
    </div>
    <h6 class="mb-3">Empty Bottle Available</h6>
    <p class="mb-3">
      This item has an empty bottle: <strong>{{ emptyBottleConfirmationData?.emptyBottleName }}</strong>
    </p>
    <div class="alert alert-info mb-3">
      <div class="row text-start">
        <div class="col-6">
          <strong>Quantity:</strong> {{ emptyBottleConfirmationData?.quantity }}
        </div>
        <div class="col-6">
          <strong>Price:</strong> {{ emptyBottleConfirmationData?.price | currency:'LKR':'symbol':'1.2-2' }}
        </div>
      </div>
    </div>
    <p class="text-muted">Would you like to add the empty bottle to this invoice?</p>
  </div>
  <div class="modal-footer justify-content-center">
    <button type="button" class="btn btn-primary me-2" (click)="confirmAddEmptyBottle()">
      <i class="fas fa-plus me-1"></i>
      Yes, Add Empty Bottle
    </button>
    <button type="button" class="btn btn-secondary" (click)="declineAddEmptyBottle()">
      <i class="fas fa-times me-1"></i>
      No, Skip
    </button>
  </div>
</ng-template>






