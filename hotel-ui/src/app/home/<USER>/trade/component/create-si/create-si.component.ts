import {AfterViewInit, Component, ElementRef, Inject, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {Item} from '../../../inventory/model/item';
import {ItemService} from '../../../inventory/service/item.service';
import {NotificationService} from '../../../../core/service/notification.service';
import {SalesInvoiceService} from '../../service/sales-invoice.service';
import {SalesInvoice} from '../../model/sales-invoice';
import {SalesInvoiceRecord} from '../../model/sales-invoice-record';
import {Customer} from '../../model/customer';
import {StockService} from '../../../inventory/service/stock.service';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {Invoice80Component} from "../invoices/invoice-80-en/invoice-80.component";
import {CustomerService} from "../../service/customer.service";
import {NewCustomerComponent} from "../customer/new-customer/new-customer.component";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {MetaData} from "../../../../core/model/metaData";
import {ChequePaymentComponent} from "../cheque-payment/cheque-payment.component";
import {DOCUMENT} from "@angular/common";
import {ManageSiComponent} from "../manage-si/manage-si.component";
import {ViewStockComponent} from "../../../inventory/components/stock/view-stock/view-stock.component";
import {ViewCashDrawerComponent} from "../cash-drawer/view-cash-drawer/view-cash-drawer.component";
import {ManageCustomerComponent} from "../customer/manage-customer/manage-customer.component";
import {Stock} from "../../../inventory/model/stock";
import {InvoiceLegalComponent} from "../invoices/invoice-legal/invoice-legal.component";
import {InvoiceLegalCustomComponent} from "../invoices/invoice-legal-custom/invoice-legal-custom.component";
import {Invoice80SnComponent} from "../invoices/invoice-80-sn/invoice-80-sn.component";
import {SettingsService} from "../../../../core/service/settings.service";
import {SilentPrintService} from "../../service/silent-print.service";
import {CompanyService} from "../../../../core/service/company.service";
import {BarcodeScannerComponent} from "../../../inventory/components/barcode-scanner/barcode-scanner.component";
import {Invoice58EnComponent} from "../invoices/invoice-58-en/invoice58-en.component";
import {Invoice76EnComponent} from "../invoices/invoice-76-en/invoice-76-en.component";
import {User} from "../../../../admin/model/user";

@Component({
  standalone: false,
  selector: 'app-sales-invoice',
  templateUrl: './create-si.component.html',
  styleUrls: ['./create-si.component.css']
})
export class CreateSiComponent implements OnInit, AfterViewInit {

  elem;

  si: SalesInvoice;
  salesInvRec: SalesInvoiceRecord;

  itemQty: number;
  sPrice: number;
  discount: number;
  isPercentage: boolean;
  isReturn: boolean;

  // Current user information
  currentUser: User;

  keyItemSearch: string;
  itemSearchList: Array<any> = [];

  keyItemNameSearch: string;
  itemNameSearchList: Array<Item> = [];

  keyCustomerSearch: string;
  customerSearchList: Array<Customer> = [];

  paymentMethods: Array<MetaData> = [];
  paymentMethodId: string;
  cashId: string;

  prices: Array<number[]> = [];
  selectedPrice: number = 0;

  // Stock selection variables
  selectedStockRecord: Stock = null;

  selectedItem: Item = new Item();
  selectedSiRecordIndex: number;
  isProcessing: boolean;

  saleTypeSale: string;
  saleTypeReturn: string;

  modalRef: BsModalRef;
  pricesModalRef: BsModalRef;

  customerModalRef: BsModalRef;
  modalRefInvoice: BsModalRef;
  duplicateIndex: string;
  defaultPaymentMethod: MetaData;

  // we have to check stock qty when adding to the bill too because of the availability issues. other user might have billed the item
  availableQty: number = 0;
  // Settings variables
  defaultDiscountMode: string;
  printerTemplate: string;
  useSilentPrint: boolean;
  minimumMarkupPercentage: number;
  allowSellingUnderCost: boolean;


  @ViewChild('quantity') qty: ElementRef;
  @ViewChild('sellingPrice') sellingPrice: ElementRef;
  @ViewChild('discountForItem') discountForItem: ElementRef;
  @ViewChild('barcodeEle') barcodeEle: ElementRef;
  @ViewChild('payment') payment: ElementRef;
  @ViewChild('tempMultiplePrice') tempMultiplePrice: TemplateRef<any>;
  @ViewChild('tempStockSelection') tempStockSelection: TemplateRef<any>;

  constructor(private itemService: ItemService, private salesInvoiceService: SalesInvoiceService,
              private notificationService: NotificationService, private modalService: BsModalService,
              private stockService: StockService, private customerService: CustomerService,
              private metaDataService: MetaDataService, private companyService: CompanyService,
              private silentPrintService: SilentPrintService,
              @Inject(DOCUMENT) private document: any,
              private settingsService: SettingsService) {
  }

  ngOnInit() {
    this.elem = document.documentElement;

    // Invoice update mode removed - only new invoice creation is allowed

    // Create a new invoice
    this.si = new SalesInvoice();
    this.si.salesInvoiceRecords = [];
    this.si.dueDate = new Date();
    this.si.totalDiscount = 0;

    // Set invoice type for bar invoices
    this.si.invoiceType = 'BAR';
    this.isProcessing = false;

    this.prices = [];

    // Load current user from localStorage
    this.loadCurrentUser();

    // Load settings from SettingsService
    this.loadSettings();

    this.isPercentage = this.defaultDiscountMode === 'percentage' ? true : false;
    this.isReturn = false;
    this.keyItemSearch = '';
    this.discount = 0;
    this.keyItemNameSearch = '';
    this.findPaymentMethods();
    this.findSalesTypes();

    // Invoice update mode removed - only new invoice creation is allowed
  }

  ngAfterViewInit() {
    this.barcodeEle.nativeElement.focus();
  }

  searchItems() {
    this.itemService.findAllByBarcodeLike(this.keyItemSearch).subscribe((result: Array<any>) => {
      return this.itemSearchList = result;
    })
  }

  openPastInvoice() {
    // Create the modal with isModal flag set to true
    const modalRef = this.modalService.show(ManageSiComponent, <ModalOptions>{
      class: 'modal-xl',
      initialState: {
        isModal: true
      }
    });

    // Pass the modalRef to the component so it can close itself
    if (modalRef && modalRef.content) {
      modalRef.content.modalRef = modalRef;
      // Ensure isModal is set to true
      modalRef.content.isModal = true;
    }
  }

  openStock() {
    const modalRef = this.modalService.show(ViewStockComponent, <ModalOptions>{
      class: 'modal-xl',
      initialState: {
        isModal: true
      }
    });

    // Pass the modalRef to the component so it can close itself
    if (modalRef && modalRef.content) {
      modalRef.content.modalRef = modalRef;
    }
  }

  openCustomer() {
    const initialState = {
      isModal: true,
      disableSetCustomer: false
    };
    this.customerModalRef = this.modalService.show(ManageCustomerComponent, <ModalOptions>{
      class: 'modal-xl',
      initialState: initialState
    });
    this.customerModalRef.content.modalRef = this.customerModalRef;
    const subscription = this.modalService.onHide.subscribe(() => {
      if (this.customerModalRef && this.customerModalRef.content && this.customerModalRef.content.customer) {
        this.si.customerNo = this.customerModalRef.content.customer.customerNo;
        this.si.customerName = this.customerModalRef.content.customer.name;
        this.keyCustomerSearch = this.customerModalRef.content.customer.name;
      }
      // Clean up subscription to avoid memory leaks
      subscription.unsubscribe();
    })
  }

  openCashier() {
    // Create the modal with isModal flag set to true
    const modalRef = this.modalService.show(ViewCashDrawerComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: {
        isModal: true
      }
    });

    // Pass the modalRef to the component so it can close itself
    if (modalRef && modalRef.content) {
      modalRef.content.modalRef = modalRef;
      // Ensure isModal is set to true
      modalRef.content.isModal = true;
    }
  }

  findPaymentMethods() {
    this.metaDataService.findByCategory('PaymentMethod').subscribe((result: Array<MetaData>) => {
      for (let meta of result) {
        if (meta.value === 'Cash') {
          this.cashId = meta.id;
          this.paymentMethodId = this.cashId;
          this.defaultPaymentMethod = meta;
        }
      }
      return this.paymentMethods = result;
    })
  }

  findSalesTypes() {
    this.metaDataService.findByCategory('SaleType').subscribe((result: Array<MetaData>) => {
      for (let meta of result) {
        if (meta.value === 'Sale') {
          this.saleTypeSale = meta.value;
        }
        if (meta.value === 'Return') {
          this.saleTypeReturn = meta.value;
        }
      }
    })
  }

  searchItemsByName() {
    this.itemService.findAllActiveByNameLike(this.keyItemNameSearch).subscribe((result: Array<Item>) => {
      return this.itemNameSearchList = result;
    })
  }

  searchCustomers() {
    this.customerService.findByNameLike(this.keyCustomerSearch).subscribe((result: Array<Customer>) => {
      return this.customerSearchList = result;
    })
  }

  calculateTotal() {
    this.si.subTotal = 0;
    this.si.totalAmount = 0;
    this.si.advancePayment = 0;
    for (let index = 0; this.si.salesInvoiceRecords.length > index; index++) {
      this.si.subTotal = this.si.subTotal + this.si.salesInvoiceRecords[index].price;
    }
    this.si.totalAmount = this.si.subTotal - this.si.totalDiscount - this.si.advancePayment;
  }

  newCustomer() {
    const initialState = {
      isModal: true
    };
    // Use a separate modal reference for the new customer modal
    const newCustomerModalRef = this.modalService.show(NewCustomerComponent, <ModalOptions>{
      class: 'modal-xl',
      initialState: initialState
    });
    newCustomerModalRef.content.modalRef = newCustomerModalRef;

    // Subscribe to modal close events to handle created customer
    const subscription = this.modalService.onHide.subscribe(() => {
      // Check if a customer was created and set it as selected
      if (newCustomerModalRef.content && newCustomerModalRef.content.customer) {
        const createdCustomer = newCustomerModalRef.content.customer;
        this.si.customerNo = createdCustomer.customerNo;
        this.si.customerName = createdCustomer.name;
        this.keyCustomerSearch = createdCustomer.name;
        this.notificationService.showSuccess('Customer created and selected successfully');
      }
      // Clean up subscription to avoid memory leaks
      subscription.unsubscribe();
    });
  }

  setPrice(price, qty) {
    this.sPrice = price;
    this.availableQty = qty;
    if (this.pricesModalRef) {
      this.pricesModalRef.hide();
    }
  }

  /**
   * Load the current user from localStorage
   */
  loadCurrentUser() {
    const currentUserData = localStorage.getItem('currentUser');
    if (currentUserData) {
      try {
        const userData = JSON.parse(currentUserData);
        this.currentUser = userData.user;
      } catch (error) {
        console.error('Error parsing current user data:', error);
      }
    }
  }


  /**
   * Check if the current user has the CASHIER role
   * @returns true if the user has the CASHIER role, false otherwise
   */
  hasUserCashierRole(): boolean {
    if (!this.currentUser || !this.currentUser.userRoles) {
      return false;
    }

    return this.currentUser.userRoles.some(role =>
      role.name === 'CASHIER'
    );
  }

  addToInvoice() {
    if (this.itemQty != 0 && null != this.selectedItem.itemCode) {

      if (this.isPercentage) {
        this.sPrice = this.sPrice - (this.sPrice * this.discount / 100)
      } else {
        this.sPrice = this.sPrice - this.discount;
      }
      // Check if selling price is less than item cost (applies to all roles if allowSellingUnderCost is false)
      if (!this.allowSellingUnderCost && this.sPrice < this.selectedItem.itemCost) {
        // Show error notification and prohibit the sale
        this.notificationService.showError(
          `Cannot sell ${this.selectedItem.itemName} below cost. Selling price (${this.sPrice}) is less than item cost (${this.selectedItem.itemCost}).`
        );
        // Focus on the price field to allow the user to correct it
        this.focusPrice();
        this.sPrice = 0;
        return; // Prevent adding the item
      }

      // Additional check for CASHIER role: selling price must be at least the minimum markup percentage higher than item cost
      if (this.hasUserCashierRole() && this.sPrice < (this.selectedItem.itemCost * (1 + this.minimumMarkupPercentage / 100))) {
        // Calculate the minimum allowed price (minimum markup percentage above cost)
        const minPrice = this.selectedItem.itemCost * (1 + this.minimumMarkupPercentage / 100);
        // Show error notification and prohibit the sale
        this.notificationService.showError(
          `For CASHIER role, selling price must be at least ${this.minimumMarkupPercentage}% higher than item cost. ` +
          `Minimum allowed price for ${this.selectedItem.itemName} is ${minPrice.toFixed(2)}.`
        );
        // Focus on the price field to allow the user to correct it
        this.focusPrice();
        return; // Prevent adding the item
      }

      this.salesInvRec = new SalesInvoiceRecord();
      this.salesInvRec.recordType = "";
      let dup = this.checkForDuplicate();
      //"0" itemCode is allowed to add duplicate because we need to let user add
      if (dup && (this.selectedItem.itemCode != "0")) {
        let response = confirm('Item already in the invoice. continue?');
        if (response) {
          this.addDuplicate();
        }
      } else {
        this.salesInvRec.itemCode = this.selectedItem.itemCode;
        this.salesInvRec.ticketType = this.selectedItem.ticketType?.value || '';
        if (this.selectedItem.itemCode == "0") {
          this.salesInvRec.itemName = this.keyItemNameSearch;
        } else {
          this.salesInvRec.itemName = this.selectedItem.itemName;
        }
        if (this.isReturn) {
          this.salesInvRec.quantity = -(this.itemQty);
          this.salesInvRec.price = -this.itemQty * this.sPrice;
          this.salesInvRec.recordType = this.saleTypeReturn;
          if (this.isPercentage) {
            this.salesInvRec.discount = this.sPrice - (this.sPrice * this.discount / 100);
          } else {
            this.salesInvRec.discount = this.sPrice - this.discount;
          }
        } else {
          // Check if this is an Empty Bottle item - special handling
          if (this.selectedItem.itemType && this.selectedItem.itemType.name === 'Empty Bottle') {
            // For Empty Bottle: negative price (customer gets money back), positive quantity (we collect bottles)
            this.salesInvRec.quantity = this.itemQty;
            this.salesInvRec.price = -(this.itemQty * this.sPrice); // Negative price (deduct from bill)
            this.salesInvRec.recordType = "Empty Bottle Return"; // Special record type
            if (this.isPercentage) {
              this.salesInvRec.discount = (this.sPrice * this.discount / 100);
            } else {
              this.salesInvRec.discount = this.discount;
            }
          } else {
            // Normal item handling
            this.salesInvRec.quantity = this.itemQty;
            this.salesInvRec.price = this.itemQty * this.sPrice;
            this.salesInvRec.recordType = this.saleTypeSale;
            if (this.isPercentage) {
              this.salesInvRec.discount = (this.sPrice * this.discount / 100);
            } else {
              this.salesInvRec.discount = this.discount;
            }
          }
        }
        this.salesInvRec.unitPrice = this.sPrice;
        if (this.selectedItem.manageStock) {
          this.salesInvRec.unitPriceOriginal = this.selectedPrice;
        }
        this.salesInvRec.barcode = this.selectedItem.barcode;
        this.salesInvRec.itemCost = this.selectedItem.itemCost;
        // Set stockId for stock-managed items
        if (this.selectedItem.manageStock && this.selectedStockRecord) {
          this.salesInvRec.stockId = this.selectedStockRecord.id;
        }
        this.si.salesInvoiceRecords.push(this.salesInvRec);
        this.discount = 0;

        // Check if the item has an empty bottle and ask for confirmation
        this.checkAndAddEmptyBottle();
      }
      this.calculateTotal();
      this.clearAddToInvoice();
      localStorage.setItem('si', JSON.stringify(this.si));
    }
  }

  loadFromMemory() {
    if (typeof localStorage !== 'undefined') {
      const savedData = localStorage.getItem('si');
      if (savedData) {
        try {
          this.si = JSON.parse(savedData);
        } catch (error) {
          console.error('Error parsing saved data:', error);
          this.notificationService.showError('Failed to load saved bill. Data is corrupted.');
        }
      } else {
        this.notificationService.showError('No bills saved in memory.');
      }
    } else {
      this.notificationService.showError('Local storage is not supported in this browser.');
    }
  }

  clear() {
    let response = confirm('Are you sure ?');
    if (response) {
      this.ngOnInit();
      this.ngAfterViewInit();
    }
  }

  clearAddToInvoice() {
    this.keyItemSearch = '';
    this.keyItemNameSearch = '';
    this.sPrice = 0;
    this.prices = [];
    this.itemQty = 0;
    this.isReturn = false;
    this.selectedItem = new Item();
    this.barcodeEle.nativeElement.focus();
  }

  focusPrice() {
    this.sellingPrice.nativeElement.focus();
  }

  focusDiscount() {
    this.discountForItem.nativeElement.focus();
  }

  focusQty() {
    this.qty.nativeElement.focus();
  }

  /*  checkAvailability() {
      if (this.selectedItem.manageStock) {
        // Use selectedPrice if available, otherwise fall back to item's selling price
        const priceToCheck = this.selectedPrice || this.selectedItem.sellingPrice || 0;
        this.stockService.findMainStockByItemCodeAndPrice(this.selectedItem.itemCode, priceToCheck).subscribe((result: Stock) => {
          this.selectedStockRecord = result; // Store the stock record for stockId assignment
          this.availableQty = result.quantity;
          if (this.selectedItem.manageStock && null === result && this.selectedItem.itemType.name !== 'Empty Bottle') {
            this.notificationService.showError('Stock Not Available');
          } else {
            if (this.selectedItem.manageStock && result.quantity < this.itemQty && !this.isReturn) {
              this.notificationService.showError('No Enough Stock Available');
            } else {
              if (this.selectedItem.manageStock && (result.quantity < result.deadStockLevel) && !this.isReturn) {
                this.notificationService.showWarning('Low Stock. Purchase immediately');
              }
              this.addToInvoice();
            }
          }
        });
      } else {
        this.addToInvoice();
      }
    }*/

  checkAvailability() {
    if (this.selectedItem.manageStock) {
      const priceToCheck = this.selectedPrice || this.selectedItem.sellingPrice || 0;

      this.stockService.findMainStockByItemCodeAndPrice(this.selectedItem.itemCode, priceToCheck)
        .subscribe((result: Stock | null) => {

          // If stock not found
          if (!result) {
            if (this.selectedItem.itemType.name === 'Empty Bottle') {
              // Proceed even if stock not found
              this.addToInvoice();
            } else {
              this.notificationService.showError('Stock Not Available');
            }
            return;
          }

          // Stock found, assign and proceed
          this.selectedStockRecord = result;
          this.availableQty = result.quantity;

          if (this.selectedItem.itemType.name !== 'Empty Bottle') {
            if (result.quantity < this.itemQty && !this.isReturn) {
              this.notificationService.showError('No Enough Stock Available');
              return;
            }

            if (result.quantity < result.deadStockLevel && !this.isReturn) {
              this.notificationService.showWarning('Low Stock. Purchase immediately');
            }
          }

          this.addToInvoice();
        });
    } else {
      this.addToInvoice();
    }
  }


  checkForDuplicate() {
    for (const index in this.si.salesInvoiceRecords) {
      if (this.si.salesInvoiceRecords[index].itemCode === this.selectedItem.itemCode) {
        this.duplicateIndex = index;
        return true;
      }
    }
    return false;
  }

  addDuplicate() {
    // Check if selling price is less than item cost (applies to all roles if allowSellingUnderCost is false)
    if (!this.allowSellingUnderCost && this.sPrice < this.selectedItem.itemCost) {
      // Show error notification and prohibit the sale
      this.notificationService.showError(
        `Cannot sell ${this.selectedItem.itemName} below cost. Selling price (${this.sPrice}) is less than item cost (${this.selectedItem.itemCost}).`
      );
      // Focus on the price field to allow the user to correct it
      this.focusPrice();
      return; // Prevent adding the item
    }

    // Additional check for CASHIER role: selling price must be at least the minimum markup percentage higher than item cost
    if (this.hasUserCashierRole() && this.sPrice < (this.selectedItem.itemCost * (1 + this.minimumMarkupPercentage / 100))) {
      // Calculate the minimum allowed price (minimum markup percentage above cost)
      const minPrice = this.selectedItem.itemCost * (1 + this.minimumMarkupPercentage / 100);
      // Show error notification and prohibit the sale
      this.notificationService.showError(
        `For CASHIER role, selling price must be at least ${this.minimumMarkupPercentage}% higher than item cost. ` +
        `Minimum allowed price for ${this.selectedItem.itemName} is ${minPrice.toFixed(2)}.`
      );
      // Focus on the price field to allow the user to correct it
      this.focusPrice();
      return; // Prevent adding the item
    }

    if ((this.si.salesInvoiceRecords[this.duplicateIndex].quantity + this.itemQty) <= this.availableQty) {
      this.si.salesInvoiceRecords[this.duplicateIndex].quantity = this.si.salesInvoiceRecords[this.duplicateIndex].quantity
        + this.itemQty;

      // Handle Empty Bottle items differently for price calculation
      if (this.selectedItem.itemType && this.selectedItem.itemType.name === 'Empty Bottle') {
        // For Empty Bottle: negative price (customer gets money back)
        this.si.salesInvoiceRecords[this.duplicateIndex].price = -(this.si.salesInvoiceRecords[this.duplicateIndex].quantity *
          this.selectedItem.sellingPrice);
      } else {
        // Normal item price calculation
        this.si.salesInvoiceRecords[this.duplicateIndex].price = this.si.salesInvoiceRecords[this.duplicateIndex].quantity *
          this.selectedItem.sellingPrice;
      }

      this.calculateTotal();
    } else {
      this.notificationService.showError("No Enough Stock Available");
    }
  }

  calculateBalance() {
    this.si.cashBalance = this.si.payment - this.si.totalAmount;
    if (this.si.cashBalance < 0) {
      this.si.balance = this.si.cashBalance * -1;
    } else {
      this.si.balance = 0;
    }
    parseFloat(this.si.cashBalance.toString()).toFixed(2);
  }

  // Handle barcode scanner enter key
  // Original handleBarcodeEnter method - commented out
  /*
  handleBarcodeEnter(event: any) {
    event.preventDefault();

    // If there's a search result and only one item, auto-select it
    if (this.itemSearchList && this.itemSearchList.length === 1) {
      this.setSelectedItem({ item: this.itemSearchList[0] });
      // Focus on quantity field after auto-selection
      setTimeout(() => {
        const quantityField = document.getElementById('qty');
        if (quantityField) {
          quantityField.focus();
        }
      }, 100);
    } else if (this.itemSearchList && this.itemSearchList.length > 1) {
      // Multiple results, let user choose from dropdown
      this.notificationService.showInfo('Multiple items found. Please select from the dropdown.');
    } else {
      // No results found, try to search by exact barcode
      if (this.keyItemSearch && this.keyItemSearch.trim()) {
        this.itemService.findAllByBarcodeLike(this.keyItemSearch.trim()).subscribe(
          (items: Array<Item>) => {
            if (items && items.length === 1) {
              this.setSelectedItem({ item: items[0] });
              // Focus on quantity field after auto-selection
              setTimeout(() => {
                const quantityField = document.getElementById('qty');
                if (quantityField) {
                  quantityField.focus();
                }
              }, 100);
            } else if (items && items.length > 1) {
              this.itemSearchList = items;
              this.notificationService.showInfo('Multiple items found. Please select from the dropdown.');
            } else {
              this.notificationService.showWarning('No item found with this barcode.');
            }
          },
          (error) => {
            this.notificationService.showWarning('Error searching for item.');
          }
        );
      } else {
        this.notificationService.showWarning('Please enter a barcode.');
      }
    }
  }
  */

  // New barcode enter handler - finds exact match using findOneByBarcode
  handleBarcodeEnterNew(event: any) {
    event.preventDefault();

    if (!this.keyItemSearch || this.keyItemSearch.trim() === '') {
      this.notificationService.showWarning('Please enter a barcode.');
      return;
    }

    // Use findOneByBarcode to get exact match
    this.itemService.findOneByBarcode(this.keyItemSearch.trim()).subscribe(
      (item: Item) => {
        if (item) {
          // Item found, auto-select it
          this.setSelectedItem({ item: item });
          // Focus on quantity field after auto-selection
          setTimeout(() => {
            const quantityField = document.getElementById('qty');
            if (quantityField) {
              quantityField.focus();
            }
          }, 100);
        } else {
          // No item found with this exact barcode
          this.notificationService.showWarning('No item found with this barcode.');
        }
      },
      (error) => {
        console.error("Error finding item by barcode:", error);
        this.notificationService.showError("Error searching item by barcode.");
      }
    );
  }

  setSelectedItem(event) {
    this.selectedItem = event.item;
    this.sPrice = this.selectedItem.sellingPrice;
    this.discount = this.selectedItem.retailDiscount;
    if (this.keyItemNameSearch.length == 0) {
      this.keyItemNameSearch = this.selectedItem.itemName;
    }
    if (this.keyItemSearch.length == 0) {
      this.keyItemSearch = this.selectedItem.barcode;
    }
    if (event.item.itemCode == "0") {
      this.keyItemNameSearch = "Item";
    }

    if (this.selectedItem.manageStock) {
      this.prices = [];
      this.stockService.findPricesByBarcodeAndWarehouse(event.item.barcode, 0).subscribe((prices: Array<number[]>) => {
        this.prices = prices;
        if (this.prices && this.prices.length > 0 && this.prices[0] && this.prices[0].length > 1) {
          this.selectedPrice = this.prices[0][1] || this.selectedItem.sellingPrice || 0;
          this.availableQty = this.prices[0][0] || 0;
        } else {
          // Fallback to item's selling price if no prices found
          this.selectedPrice = this.selectedItem.sellingPrice || 0;
          this.availableQty = 0;
        }
        if (this.prices.length > 1) {
          this.pricesModalRef = this.modalService.show(this.tempMultiplePrice, {
            class: 'modal-md',
            animated: true
          });
        }
      })
    }

    this.qty.nativeElement.value = '';
    this.qty.nativeElement.focus();

  }

  gotoPayment() {
    if (this.keyItemSearch.length == 0) {
      this.payment.nativeElement.focus();
    }
  }

  saveByEnter() {
    if (this.si.payment != null) {
      this.save(true);
    }
  }

  hidePricesModal() {
    if (this.pricesModalRef) {
      this.pricesModalRef.hide();
    }
  }

  setSelectedCustomer(event) {
    this.si.customerNo = event.item.customerNo;
    this.si.customerName = event.item.name;
  }

  selectRow(index) {
    this.selectedSiRecordIndex = index;
  }

  removeRow(index) {
    this.si.salesInvoiceRecords.splice(index, 1);
    this.calculateTotal();
  }

  setPaymentMethod(event) {
    this.si.paymentMethod = new MetaData();
    this.si.paymentMethod.id = event.target.value;

    if (this.si.paymentMethod != null && this.si.paymentMethod.id != this.cashId) {
      // Use a separate modal reference for the cheque payment modal
      const chequeModalRef = this.modalService.show(ChequePaymentComponent, <ModalOptions>{
        class: 'modal-md',
        ignoreBackdropClick: true,
        initialState: {
          isModal: true,
          chequeType: 'RECEIVED', // Always set to RECEIVED for sales invoices
          hideTypeSelection: true // Hide the type selection UI
        }
      });
      chequeModalRef.content.modalRef = chequeModalRef;
      chequeModalRef.content.totalAmount = this.si.totalAmount;
      chequeModalRef.content.cashAmount = this.si.payment;
      if (this.si.customerNo != null && this.si.customerName != null) {
        // Create a customer object for the cheque
        const customer = new Customer();
        customer.customerNo = this.si.customerNo;
        customer.name = this.si.customerName;
        chequeModalRef.content.cheque.customer = customer;
      }
      const subscription = this.modalService.onHide.subscribe(() => {
        if (chequeModalRef && chequeModalRef.content && chequeModalRef.content.cheque &&
          chequeModalRef.content.cheque.chequeNo && chequeModalRef.content.cheque.chequeAmount > 0 &&
          chequeModalRef.content.cheque.customer && chequeModalRef.content.cheque.customer.name != 'Default Customer') {
          this.si.cashlessAmount = chequeModalRef.content.cheque.chequeAmount;
          this.si.cashAmount = chequeModalRef.content.cashAmount;
          this.si.cardOrVoucherNo = chequeModalRef.content.cheque.chequeNo;
          this.si.cheque = chequeModalRef.content.cheque;
        }
        // Clean up subscription to avoid memory leaks
        subscription.unsubscribe();
      })
    }
  }

  /**
   * Save the invoice and print it based on the print parameter
   * @param print Whether to print the invoice after saving
   */
  save(print: boolean) {
    this.saveOrUpdate(print);
  }

  /**
   * Method to handle saving invoices
   * @param print Whether to print the invoice after saving
   */
  saveOrUpdate(print: boolean) {
    if (this.isProcessing) {
      return; // Prevent multiple operations
    }

    this.isProcessing = true;

    // Common validations for both save and update
    if (!this.si.paymentMethod || undefined === this.si.paymentMethod.id) {
      this.si.paymentMethod = this.defaultPaymentMethod;
    }

    if (this.si.totalAmount > this.si.payment) {
      if (!this.si.customerNo) {
        this.notificationService.showError('Cannot create this bill without a Customer');
        this.isProcessing = false;
        return;
      }
      if (this.si.customerName == 'Default Customer') {
        this.notificationService.showError('Cannot create this bill without a Real Customer');
        this.isProcessing = false;
        return;
      }
    }

    // Only save is allowed - no update functionality
    const serviceCall = this.salesInvoiceService.save(this.si);

    serviceCall.subscribe((result: any) => {
      if (null != result && result.code === 200) {
        this.notificationService.showSuccess(result.message);

        // If this is opened in a modal, close it after saving/updating
        if (this.modalRef) {
          this.close();
          return;
        }

        // Otherwise continue with normal flow
        if (print) { // Print if requested
          // Check if silent printing is enabled in settings
          if (this.useSilentPrint) {
            // Use silent printing
            this.printInvoiceSilently(result.data);
          } else {
            // Use regular printing
            this.printInvoiceRegular(result.data);
          }
        }

        this.ngOnInit();
        this.isProcessing = false;
        localStorage.removeItem('si');
      } else {
        this.notificationService.showError(result.message || 'Failed to save invoice');
        console.log(result.data);
        this.isProcessing = false;
      }
    }, error => {
      console.error('Error saving invoice:', error);
      this.notificationService.showError(`Failed to save invoice: ${error.message || 'Unknown error'}`);
      this.isProcessing = false;
    });
  }

  /**
   * Save the invoice and print it silently
   */
  saveAndSilentPrint() {
    // Force silent printing by setting useSilentPrint to true temporarily
    const originalSilentPrintSetting = this.useSilentPrint;
    this.useSilentPrint = true;

    // Call the unified save method with print=true
    this.saveOrUpdate(true);

    // Restore the original silent print setting
    this.useSilentPrint = originalSilentPrintSetting;
  }

  /**
   * Print invoice using the regular modal approach
   * @param invoiceNo The invoice number to print
   */
  printInvoiceRegular(invoiceNo: string) {
    // Use the traditional modal approach
    const initialState = {
      isModal: true
    };

    // Use the printer template from settings
    switch (this.printerTemplate) {
      case "80mm_English":
        this.modalRefInvoice = this.modalService.show(Invoice80Component, {
          class: 'modal-sm',
          initialState: initialState
        });
        break;
      case "80mm_Sinhala":
        this.modalRefInvoice = this.modalService.show(Invoice80SnComponent, {
          class: 'modal-sm',
          initialState: initialState
        });
        break;
      case "76mm_English":
        this.modalRefInvoice = this.modalService.show(Invoice76EnComponent, {
          class: 'modal-sm',
          initialState: initialState
        });
        break;
      case "58mm_English":
        this.modalRefInvoice = this.modalService.show(Invoice58EnComponent, {
          class: 'modal-sm',
          initialState: initialState
        });
        break;
      case "Legal_English":
        this.modalRefInvoice = this.modalService.show(InvoiceLegalComponent, {
          class: 'modal-xl',
          initialState: initialState
        });
        break;
      case "Legal_Customised_English":
        this.modalRefInvoice = this.modalService.show(InvoiceLegalCustomComponent, {
          class: 'modal-xl',
          initialState: initialState
        });
        break;
      default:
        // Default to 58mm English if no template is specified
        this.modalRefInvoice = this.modalService.show(Invoice58EnComponent, {
          class: 'modal-sm',
          initialState: initialState
        });
        break;
    }

    // Pass the modalRef to the component so it can close itself
    if (this.modalRefInvoice && this.modalRefInvoice.content) {
      this.modalRefInvoice.content.modalRef = this.modalRefInvoice;
    }
    this.modalRefInvoice.content.invoiceNo = invoiceNo;
    this.modalRefInvoice.content.findInvoice();
    this.modalRefInvoice.content.pastBill = false;
  }

  /**
   * Print invoice silently without showing the print dialog
   * @param invoiceNo The invoice number to print
   */
  printInvoiceSilently(invoiceNo: string) {
    this.createAndPrintInvoiceSilently(invoiceNo);
  }

  /**
   * Creates a hidden invoice element and prints it silently
   * @param invoiceNo The invoice number to print
   * @private Internal method used by printInvoiceSilently
   */
  private createAndPrintInvoiceSilently(invoiceNo: string) {
    // Check if silent printing is enabled in settings
    if (this.useSilentPrint) {
      // Use the silent print service
      this.silentPrintService.printInvoice(invoiceNo, this.printerTemplate);
    } else {
      // Fetch the invoice data
      this.salesInvoiceService.findByInvoiceNo(invoiceNo).subscribe((invoice: SalesInvoice) => {
        // Also fetch company data
        this.companyService.findCompany().subscribe((company) => {
          // Create a direct print using the regular method
          this.generateInvoiceHtmlAndPrint(invoice, company);
        });
      });
    }
  }

  /**
   * Prints the invoice silently using the appropriate invoice component
   * @param invoice The invoice data
   * @param company The company data
   */
  private generateInvoiceHtmlAndPrint(invoice: SalesInvoice, company: any) {
    // Instead of generating HTML directly, we'll use the silentPrintService to print
    // the appropriate component based on the environment setting
    const invoiceNo = invoice.invoiceNo;

    // Open the appropriate invoice component in a hidden iframe and print it
    this.printInvoiceRegular(invoiceNo);
  }

  /**
   * Load settings from SettingsService
   */
  private loadSettings() {
    // Use the SettingsService to get all settings
    this.defaultDiscountMode = this.settingsService.getDefaultDiscountMode();
    this.printerTemplate = this.settingsService.getSetting('printerTemplate', '80mm_English');
    this.useSilentPrint = this.settingsService.useSilentPrint();
    this.minimumMarkupPercentage = this.settingsService.getMinimumMarkupPercentage();
    this.allowSellingUnderCost = this.settingsService.isSellingUnderCostAllowed();

    console.log('Loaded settings:', {
      defaultDiscountMode: this.defaultDiscountMode,
      printerTemplate: this.printerTemplate,
      useSilentPrint: this.useSilentPrint,
      minimumMarkupPercentage: this.minimumMarkupPercentage,
      allowSellingUnderCost: this.allowSellingUnderCost
    });
  }

  removeCheque() {
    this.si.cheque = null;
  }

  // Invoice update functionality removed - only new invoice creation is allowed

  /**
   * Load an invoice for editing
   * @param invoiceNo The invoice number to load
   */
  loadInvoice(invoiceNo: string) {
    // Show loading notification or spinner
    this.isProcessing = true;

    this.salesInvoiceService.findByInvoiceNo(invoiceNo).subscribe(
      (invoice: SalesInvoice) => {
        this.isProcessing = false;

        if (invoice) {
          this.si = invoice;
          // Invoice update mode removed - only new invoice creation is allowed

          // If the invoice has a customer, load the customer details
          if (this.si.customerNo) {
            this.customerService.findByCustomerNo(this.si.customerNo).subscribe(
              (customer: any) => {
                if (customer) {
                  // Update customer-related fields
                  this.si.customerName = customer.name;
                  this.keyCustomerSearch = customer.name;
                }
                this.calculateTotal();
                this.notificationService.showSuccess('Invoice loaded for editing');
              },
              error => {
                this.calculateTotal();
                this.notificationService.showWarning('Invoice loaded, but customer details could not be retrieved');
              }
            );
          } else {
            this.calculateTotal();
            this.notificationService.showSuccess('Invoice loaded for editing');
          }
        } else {
          this.notificationService.showError('Invoice not found. Please try again with a valid invoice number.');
        }
      },
      error => {
        this.isProcessing = false;
        this.notificationService.showError('Failed to load invoice. Please check your connection and try again.');
      }
    );
  }

  // Invoice update functionality removed - only new invoice creation is allowed

  /* Close fullscreen */
  closeFullscreen() {
    if (document.exitFullscreen) {
      this.document.exitFullscreen();
    } else if (this.document.mozCancelFullScreen) {
      this.document.mozCancelFullScreen();
    } else if (this.document.webkitExitFullscreen) {
      this.document.webkitExitFullscreen();
    } else if (this.document.msExitFullscreen) {
      this.document.msExitFullscreen();
    }
  }

  /**
   * Close the modal when this component is opened as a modal
   */
  close() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }


  /**
   * Open barcode scanner modal
   */
  openBarcodeScanner() {
    const modalOptions: ModalOptions = {
      class: 'modal-md',
      initialState: {
        isModal: true
      }
    };
    // Use a separate modal reference for the barcode scanner modal
    const barcodeScannerModalRef = this.modalService.show(BarcodeScannerComponent, modalOptions);
    // Pass the modalRef to the component so it can close itself
    if (barcodeScannerModalRef && barcodeScannerModalRef.content) {
      barcodeScannerModalRef.content.modalRef = barcodeScannerModalRef;
    }

    // Subscribe to the barcodeScanned event from the modal component
    barcodeScannerModalRef.content.barcodeScanned.subscribe((barcode: string) => {
      if (barcode) {
        this.keyItemSearch = barcode;

        // Use findOneByBarcode to get exact match
        this.itemService.findOneByBarcode(barcode).subscribe(
          (item: Item) => {
            if (item) {
              // Item found, auto-select it
              this.setSelectedItem({ item: item });
              // Focus on quantity field after auto-selection
              setTimeout(() => {
                const quantityField = document.getElementById('qty');
                if (quantityField) {
                  quantityField.focus();
                }
              }, 100);
            } else {
              // No item found with this exact barcode
              this.notificationService.showWarning('No item found with this barcode.');
            }
          },
          (error) => {
            console.error("Error finding item by barcode:", error);
            this.notificationService.showError("Error searching item by barcode.");
          }
        );
      }
    });
  }

  /**
   * Check if the selected item has an empty bottle and ask for confirmation to add it
   */
  checkAndAddEmptyBottle(): void {
    // Only check for non-return sales and items that have empty bottles
    if (!this.isReturn && this.selectedItem && this.selectedItem.hasEmpty && this.selectedItem.emptyBottle) {
      const confirmMessage = `This item has a returnable empty bottle (${this.selectedItem.emptyBottle.itemName}). Would you like to add it to the invoice?`;

      if (confirm(confirmMessage)) {
        // Create a new sales invoice record for the empty bottle
        const emptyBottleRecord = new SalesInvoiceRecord();
        emptyBottleRecord.itemCode = this.selectedItem.emptyBottle.itemCode;
        emptyBottleRecord.barcode = this.selectedItem.emptyBottle.barcode;
        emptyBottleRecord.itemName = this.selectedItem.emptyBottle.itemName;
        emptyBottleRecord.quantity = this.itemQty; // Same quantity as the main item
        emptyBottleRecord.unitPrice = this.selectedItem.emptyBottle.sellingPrice;
        emptyBottleRecord.price = -(this.itemQty * this.selectedItem.emptyBottle.sellingPrice); // Negative price (deduct from bill)
        emptyBottleRecord.recordType = "Empty Bottle Return";
        emptyBottleRecord.discount = 0;
        emptyBottleRecord.itemCost = this.selectedItem.emptyBottle.itemCost;
        emptyBottleRecord.ticketType = this.selectedItem.emptyBottle.ticketType?.value || '';

        // Add the empty bottle record to the invoice
        this.si.salesInvoiceRecords.push(emptyBottleRecord);

        // Show success message
        this.notificationService.showSuccess(`Empty bottle (${this.selectedItem.emptyBottle.itemName}) added to invoice`);
      }
    }
  }
}
