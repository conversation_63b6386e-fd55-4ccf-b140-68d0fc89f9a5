/**
 * Payment method and type constants to replace MetaData usage
 * This will be used throughout the system for consistent payment handling
 */
export class PaymentConstants {

  // Payment Methods
  public static readonly PAYMENT_METHOD_CASH = 'CASH';
  public static readonly PAYMENT_METHOD_BANK_TRANSFER = 'BANK_TRANSFER';
  public static readonly PAYMENT_METHOD_CHEQUE = 'CHEQUE';
  public static readonly PAYMENT_METHOD_PETTY_CASH = 'PETTY_CASH';
  public static readonly PAYMENT_METHOD_CARD = 'CARD';

  // Payment Types for Sales
  public static readonly SALES_PAYMENT_CASH = 'SALES_CASH';
  public static readonly SALES_PAYMENT_CARD = 'SALES_CARD';
  public static readonly SALES_PAYMENT_CHEQUE = 'SALES_CHEQUE';

  // Payment Types for Purchases
  public static readonly PURCHASE_PAYMENT_BANK_TRANSFER = 'PURCHASE_BANK_TRANSFER';
  public static readonly PURCHASE_PAYMENT_CHEQUE = 'PURCHASE_CHEQUE';
  public static readonly PURCHASE_PAYMENT_PETTY_CASH = 'PURCHASE_PETTY_CASH';
  public static readonly PURCHASE_PAYMENT_CASH = 'PURCHASE_CASH';

  // Account Types (Generic for all account types)
  public static readonly ACCOUNT_TYPE_BANK = 'BANK';
  public static readonly ACCOUNT_TYPE_SUPPLIER = 'SUPPLIER';
  public static readonly ACCOUNT_TYPE_CUSTOMER = 'CUSTOMER';
  public static readonly ACCOUNT_TYPE_CASH = 'CASH';
  public static readonly ACCOUNT_TYPE_PETTY_CASH = 'PETTY_CASH';

  // Legacy bank account types (for backward compatibility)
  public static readonly ACCOUNT_TYPE_SAVINGS = 'SAVINGS';
  public static readonly ACCOUNT_TYPE_CURRENT = 'CURRENT';
  public static readonly ACCOUNT_TYPE_FIXED_DEPOSIT = 'FIXED_DEPOSIT';
  public static readonly ACCOUNT_TYPE_FUND = 'FUND';

  // Transaction Types
  public static readonly TRANSACTION_TYPE_DEBIT = 'DEBIT';
  public static readonly TRANSACTION_TYPE_CREDIT = 'CREDIT';

  // Cash Record Types
  public static readonly CASH_TYPE_IN = 'CASH_IN';
  public static readonly CASH_TYPE_OUT = 'CASH_OUT';

  // Cash Record Purposes
  public static readonly CASH_PURPOSE_DAY_START = 'DAY_START';
  public static readonly CASH_PURPOSE_DAY_END = 'DAY_END';
  public static readonly CASH_PURPOSE_PURCHASING = 'PURCHASING';
  public static readonly CASH_PURPOSE_SALES = 'SALES';
  public static readonly CASH_PURPOSE_WITHDRAWAL = 'WITHDRAWAL';

  // Cheque Status
  public static readonly CHEQUE_STATUS_PENDING = 'PENDING';
  public static readonly CHEQUE_STATUS_CLEARED = 'CLEARED';
  public static readonly CHEQUE_STATUS_BOUNCED = 'BOUNCED';
  public static readonly CHEQUE_STATUS_CANCELLED = 'CANCELLED';

  // Payment Status
  public static readonly PAYMENT_STATUS_PENDING = 'PENDING';
  public static readonly PAYMENT_STATUS_PAID = 'PAID';
  public static readonly PAYMENT_STATUS_PARTIAL = 'PARTIAL';
  public static readonly PAYMENT_STATUS_CANCELLED = 'CANCELLED';

  // Reference Types
  public static readonly REFERENCE_TYPE_PURCHASE_INVOICE = 'PURCHASE_INVOICE';
  public static readonly REFERENCE_TYPE_SALES_INVOICE = 'SALES_INVOICE';
  public static readonly REFERENCE_TYPE_DEPOSIT = 'DEPOSIT';
  public static readonly REFERENCE_TYPE_WITHDRAWAL = 'WITHDRAWAL';
  public static readonly REFERENCE_TYPE_OPENING_BALANCE = 'OPENING_BALANCE';

  // Bank Transaction Types
  public static readonly BANK_TRANSACTION_PURCHASE_PAYMENT = 'PURCHASE_PAYMENT';
  public static readonly BANK_TRANSACTION_DEPOSIT = 'DEPOSIT';
  public static readonly BANK_TRANSACTION_WITHDRAWAL = 'WITHDRAWAL';
  public static readonly BANK_TRANSACTION_TRANSFER = 'TRANSFER';
  public static readonly BANK_TRANSACTION_OPENING_BALANCE = 'OPENING_BALANCE';

  // Validation Messages
  public static readonly MSG_INSUFFICIENT_BALANCE = 'Insufficient balance';
  public static readonly MSG_INVALID_PAYMENT_METHOD = 'Invalid payment method';
  public static readonly MSG_PAYMENT_SUCCESSFUL = 'Payment processed successfully';
  public static readonly MSG_PAYMENT_FAILED = 'Payment processing failed';

  // Get purchase payment methods for dropdown
  public static getPurchasePaymentMethods(): Array<{value: string, label: string}> {
    return [
      { value: PaymentConstants.PURCHASE_PAYMENT_BANK_TRANSFER, label: 'Bank Transfer' },
      { value: PaymentConstants.PURCHASE_PAYMENT_CHEQUE, label: 'Cheque' },
      { value: PaymentConstants.PURCHASE_PAYMENT_PETTY_CASH, label: 'Petty Cash' },
      { value: PaymentConstants.PURCHASE_PAYMENT_CASH, label: 'Cash' }
    ];
  }

  // Get sales payment methods for dropdown
  public static getSalesPaymentMethods(): Array<{value: string, label: string}> {
    return [
      { value: PaymentConstants.SALES_PAYMENT_CASH, label: 'Cash' },
      { value: PaymentConstants.SALES_PAYMENT_CARD, label: 'Card' },
      { value: PaymentConstants.SALES_PAYMENT_CHEQUE, label: 'Cheque' }
    ];
  }

  // Get account types for dropdown
  public static getAccountTypes(): Array<{value: string, label: string}> {
    return [
      { value: PaymentConstants.ACCOUNT_TYPE_BANK, label: 'Bank Account' },
      { value: PaymentConstants.ACCOUNT_TYPE_SUPPLIER, label: 'Supplier Account' },
      { value: PaymentConstants.ACCOUNT_TYPE_CUSTOMER, label: 'Customer Account' },
      { value: PaymentConstants.ACCOUNT_TYPE_CASH, label: 'Cash Account' },
      { value: PaymentConstants.ACCOUNT_TYPE_PETTY_CASH, label: 'Petty Cash Account' }
    ];
  }

  // Get legacy bank account types for backward compatibility
  public static getLegacyBankAccountTypes(): Array<{value: string, label: string}> {
    return [
      { value: PaymentConstants.ACCOUNT_TYPE_SAVINGS, label: 'Savings Account' },
      { value: PaymentConstants.ACCOUNT_TYPE_CURRENT, label: 'Current Account' },
      { value: PaymentConstants.ACCOUNT_TYPE_FIXED_DEPOSIT, label: 'Fixed Deposit' },
      { value: PaymentConstants.ACCOUNT_TYPE_FUND, label: 'Fund' }
    ];
  }
}
