import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ReportApiConstants } from '../report-constants';
import { SalesInvoice } from '../../trade/model/sales-invoice';

@Injectable({
  providedIn: 'root'
})
export class SalesInvoiceReportService {

  constructor(private http: HttpClient) { }

  /**
   * Get all sales invoices.
   * @returns Observable of SalesInvoice array
   */
  findAll(): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_ALL, {
      params: {
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Find sales invoices by date range.
   * @param startDate Start date (inclusive)
   * @param endDate End date (inclusive)
   * @returns Observable of SalesInvoice array
   */
  findByDateRange(startDate: string, endDate: string): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_DATE_RANGE, {
      params: {
        startDate: startDate,
        endDate: endDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Find today's sales invoices.
   * @returns Observable of SalesInvoice array
   */
  findTodayInvoices(): Observable<SalesInvoice[]> {
    const today = new Date();
    const formattedDate = this.formatDate(today);

    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_DATE_RANGE, {
      params: {
        startDate: formattedDate,
        endDate: formattedDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Find today's sales invoices for a specific cashier.
   * @param username Cashier username
   * @returns Observable of SalesInvoice array
   */
  findTodayInvoicesByCashier(username: string): Observable<SalesInvoice[]> {
    const today = new Date();
    const formattedDate = this.formatDate(today);

    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_USER_AND_DATE_RANGE, {
      params: {
        username: username,
        startDate: formattedDate,
        endDate: formattedDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Format date to YYYY-MM-DD
   * @param date Date to format
   * @returns Formatted date string
   */
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Find sales invoices by customer.
   * @param customerId Customer ID
   * @returns Observable of SalesInvoice array
   */
  findByCustomer(customerId: string): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_CUSTOMER, {
      params: {
        customerId: customerId,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Find sales invoices by payment status.
   * @param statusId Payment status ID
   * @returns Observable of SalesInvoice array
   */
  findByStatus(statusId: string): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_STATUS, {
      params: {
        statusId: statusId,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Find sales invoices by invoice type.
   * @param invoiceType Invoice type
   * @returns Observable of SalesInvoice array
   */
  findByInvoiceType(invoiceType: string): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.API_URL + 'salesInvoiceReport/findByInvoiceType', {
      params: {
        invoiceType: invoiceType,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Find sales invoices by cashier (drawerNo).
   * @param drawerNo CashDrawer drawerNo
   * @returns Observable of SalesInvoice array
   */
  findByCashier(drawerNo: string): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.API_URL + 'salesInvoiceReport/findByCashier', {
      params: {
        drawerNo: drawerNo,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Find sales invoices by cash drawer and date range.
   * @param cashDrawer CashDrawer object or ID
   * @param startDate Start date (inclusive)
   * @param endDate End date (inclusive)
   * @returns Observable of SalesInvoice array
   */
  findByCashierAndDateRange(cashDrawer: any, startDate: string, endDate: string): Observable<SalesInvoice[]> {
    let drawerNo;

    if (typeof cashDrawer === 'string') {
      drawerNo = cashDrawer;
    } else if (cashDrawer && cashDrawer.drawerNo) {
      drawerNo = cashDrawer.drawerNo;
    } else {
      drawerNo = cashDrawer.id || cashDrawer;
    }

    return this.http.get<any>(ReportApiConstants.API_URL + 'salesInvoiceReport/findByCashierAndDateRange', {
      params: {
        drawerNo: drawerNo,
        startDate: startDate,
        endDate: endDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Find sales invoices by cashier user and date range.
   * @param user User with cashier role
   * @param startDate Start date (inclusive)
   * @param endDate End date (inclusive)
   * @returns Observable of SalesInvoice array
   */
  findByCashierUserAndDateRange(user: any, startDate: string, endDate: string): Observable<SalesInvoice[]> {
    const username = user.username || user;

    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_USER_AND_DATE_RANGE, {
      params: {
        username: username,
        startDate: startDate,
        endDate: endDate,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }

  /**
   * Export sales invoices to Excel.
   * @param startDate Start date (inclusive) or null for all
   * @param endDate End date (inclusive) or null for all
   * @param customerId Customer ID or null for all
   * @param statusId Status ID or null for all
   * @param cashierUserName Username of cashier or null for all
   * @returns Observable of Blob
   */
  exportToExcel(startDate?: string, endDate?: string, customerId?: string, statusId?: string, cashierUserName?: string): Observable<Blob> {
    let params: any = {};

    if (startDate && endDate) {
      params.startDate = startDate;
      params.endDate = endDate;
    }

    if (customerId) {
      params.customerId = customerId;
    }

    if (statusId) {
      params.statusId = statusId;
    }

    if (cashierUserName) {
      params.cashierUserName = cashierUserName; // This matches the backend parameter name
    }

    return this.http.get(ReportApiConstants.SALES_INVOICE_REPORT_EXPORT_TO_EXCEL, {
      params: params,
      responseType: 'blob'
    });
  }

  /**
   * Export sales invoices to PDF.
   * @param startDate Start date (inclusive) or null for all
   * @param endDate End date (inclusive) or null for all
   * @param customerId Customer ID or null for all
   * @param statusId Status ID or null for all
   * @param cashierUserName Username of cashier or null for all
   * @returns Observable of Blob
   */
  exportToPdf(startDate?: string, endDate?: string, customerId?: string, statusId?: string, cashierUserName?: string): Observable<Blob> {
    let params: any = {};

    if (startDate && endDate) {
      params.startDate = startDate;
      params.endDate = endDate;
    }

    if (customerId) {
      params.customerId = customerId;
    }

    if (statusId) {
      params.statusId = statusId;
    }

    if (cashierUserName) {
      params.cashierUserName = cashierUserName; // This matches the backend parameter name
    }

    return this.http.get(ReportApiConstants.SALES_INVOICE_REPORT_EXPORT_TO_PDF, {
      params: params,
      responseType: 'blob'
    });
  }

  /**
   * Find sales invoices by user only.
   * @param username Username to filter by
   * @returns Observable of SalesInvoice array
   */
  findByUser(username: string): Observable<SalesInvoice[]> {
    return this.http.get<any>(ReportApiConstants.SALES_INVOICE_REPORT_FIND_BY_USER, {
      params: {
        username: username,
        page: '0',
        pageSize: '10000' // Large number to effectively disable pagination
      }
    }).pipe(
      map(response => {
        if (Array.isArray(response)) {
          return response;
        }
        if (response && response.content && Array.isArray(response.content)) {
          return response.content;
        }
        return [];
      })
    );
  }
}
