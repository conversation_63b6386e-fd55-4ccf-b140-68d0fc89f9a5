import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {ReportApiConstants} from '../report-constants';

@Injectable({
  providedIn: 'root'
})
export class EmpCommissionReportService {

  constructor(private http: HttpClient) {
  }

  public getAllCommissions() {
    return this.http.get(ReportApiConstants.FIND_ALL_COMMISSION);
  }

  public findByJobNo(jobNo) {
    return this.http.get(ReportApiConstants.FIND_COMMISSION_JOB_NO, {params: {jobNo: jobNo}});
  }

  public findByEmpIdAndDateBetween(empId, sDate, eDate) {
    return this.http.get(ReportApiConstants.FIND_COMMISSION_EMP_ID_DATE_BETWEEN, {
      params: {
        empId: empId,
        sDate: sDate,
        eDate: eDate
      }
    });
  }

}
