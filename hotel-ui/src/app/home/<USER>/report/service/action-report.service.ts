import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ReportApiConstants } from '../report-constants';
import { Action } from '../model/action';
import { MetaData } from '../../../core/model/metaData';

@Injectable({
  providedIn: 'root'
})
export class ActionReportService {

  constructor(private http: HttpClient) { }

  /**
   * Get all actions with pagination
   * @param page Page number (0-based)
   * @param pageSize Number of items per page
   * @returns Observable of paginated actions
   */
  findAll(page: number, pageSize: number): Observable<any> {
    return this.http.get(ReportApiConstants.ACTION_REPORT_FIND_ALL, {
      params: {
        page: page.toString(),
        pageSize: pageSize.toString()
      }
    });
  }

  /**
   * Get all action types (MetaData with category "Action")
   * @returns Observable of action types
   */
  getActionTypes(): Observable<MetaData[]> {
    return this.http.get<MetaData[]>(ReportApiConstants.ACTION_REPORT_GET_ACTION_TYPES);
  }

  /**
   * Find actions with filters
   * @param type Action type (optional)
   * @param reference Reference text to search for (optional)
   * @param remark Remark text to search for (optional)
   * @param startDate Start date for date range filter (optional)
   * @param endDate End date for date range filter (optional)
   * @param page Page number (0-based)
   * @param pageSize Number of items per page
   * @returns Observable of paginated filtered actions
   */
  findWithFilters(
    type: string = null,
    reference: string = null,
    remark: string = null,
    startDate: Date = null,
    endDate: Date = null,
    page: number = 0,
    pageSize: number = 10
  ): Observable<any> {
    // Build params object with only the provided filters
    const params: any = {
      page: page.toString(),
      pageSize: pageSize.toString()
    };

    if (type) {
      params.type = type;
    }

    if (reference) {
      params.reference = reference;
    }

    if (remark) {
      params.remark = remark;
    }

    if (startDate) {
      params.startDate = startDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    }

    if (endDate) {
      params.endDate = endDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    }

    return this.http.get(ReportApiConstants.ACTION_REPORT_FIND_WITH_FILTERS, { params });
  }
}
