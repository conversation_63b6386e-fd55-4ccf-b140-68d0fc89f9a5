<div class="container-fluid professional-container">
<div class="modal-header">
  <h4 class="modal-title">Item Report Filters</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="closeModal()"></button>
</div>

<div class="modal-body">
  <form [formGroup]="filterForm" class="form-professional">

    <!-- Advanced Filters -->
    <div class="mb-3 mb-3-professional">
      <label class="form-label fw-bold">Advanced Filters</label>
      <div class="row">
        <div class="col-6">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" formControlName="active" [value]="true" id="activeFilter">
            <label class="form-check-label" for="activeFilter">
              Active Items Only
            </label>
          </div>
        </div>
        <div class="col-6">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" formControlName="manageStock" [value]="true" id="manageStockFilter">
            <label class="form-check-label" for="manageStockFilter">
              Manage Stock Only
            </label>
          </div>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-6">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" formControlName="wholesale" [value]="true" id="wholesaleFilter">
            <label class="form-check-label" for="wholesaleFilter">
              Wholesale Items
            </label>
          </div>
        </div>
        <div class="col-6">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" formControlName="retail" [value]="true" id="retailFilter">
            <label class="form-check-label" for="retailFilter">
              Retail Items
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Sort Options -->
    <div class="row">
      <div class="col-6">
        <div class="mb-3 mb-3-professional">
          <label for="sortBy" class="form-label fw-bold">Sort By</label>
          <select class="form-control form-select-fixed form-select" style="min-height: 38px; line-height: 1.5; padding: 0.5rem 0.75rem;" style="text-overflow: ellipsis;" formControlName="sortBy">
            <option *ngFor="let option of sortOptions" [value]="option.value">
              {{ option.label }}
            </option>
          </select>
        </div>
      </div>
      <div class="col-6">
        <div class="mb-3 mb-3-professional">
          <label for="sortDirection" class="form-label fw-bold">Sort Direction</label>
          <select class="form-control form-select-fixed form-select" style="min-height: 38px; line-height: 1.5; padding: 0.5rem 0.75rem;" style="text-overflow: ellipsis;" formControlName="sortDirection">
            <option *ngFor="let direction of sortDirections" [value]="direction.value">
              {{ direction.label }}
            </option>
          </select>
        </div>
      </div>
    </div>

  </form>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-primary-outline-secondary ms-2" (click)="clearFilters()" style="margin-right: 0.5rem;"><i class="fa fa-search"></i> Clear All
  </button>
      <button type="button" class="btn btn-primary btn-secondary ms-2" (click)="modalRef.hide()" style="margin-right: 0.5rem;">
    <i class="fa fa-times"></i> Cancel
  </button>
  <button type="button" class="btn btn-primary" (click)="applyFilters()">
    <i class="fa fa-check"></i> Apply Filters
  </button>
</div>

</div>
