<div class="container-fluid no-scroll" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">VIEW ALL ITEMS</h2>
    <button *ngIf="isModal" type="button" class="btn-close" aria-label="Close"  (click)="closeModal()"></button>
  </div>
  <!-- Professional Filter Section -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-white border-bottom">
      <h6 class="mb-0 text-primary"><i class="fas fa-filter me-2"></i>Search & Filter Options</h6>
    </div>
    <div class="card-body">
      <div class="row g-3 align-items-end">
        <div class="col-12 col-sm-6 col-lg-3">
          <label class="form-label fw-semibold mb-2">Search By Name</label>
          <div class="input-group">
            <input [(ngModel)]="keyItemSearch"
                   [typeahead]="itemSearched"
                   (typeaheadLoading)="loadItems()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="15"
                   typeaheadOptionField="itemName"
                   placeholder="Search By Name"
                   autocomplete="off"
                   class="form-control" name="searchItem">
            <button class="btn btn-primary" type="button">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-3">
          <label class="form-label fw-semibold mb-2">Search By Barcode</label>
          <div class="input-group">
            <input [(ngModel)]="barcode"
                   [typeahead]="itemSearched"
                   (typeaheadLoading)="loadItemByCode()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="15"
                   typeaheadOptionField="barcode"
                   autocomplete="off"
                   placeholder="Search By Barcode"
                   class="form-control" name="barcode">
            <button class="btn btn-primary" type="button">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
        <div class="col-12 col-sm-6 col-lg-2">
          <label class="form-label fw-semibold mb-2">Category</label>
          <input [(ngModel)]="keyItemCategory"
                 [typeahead]="itemCategories"
                 (typeaheadLoading)="loadItemCategories()"
                 (typeaheadOnSelect)="setSelectedItemCategory($event)"
                 [typeaheadOptionsLimit]="15"
                 typeaheadOptionField="categoryName"
                 placeholder="Category"
                 autocomplete="off"
                 required #category="ngModel"
                 class="form-control" name="itemCategory">
        </div>
        <div class="col-12 col-sm-6 col-lg-2">
          <label class="form-label fw-semibold mb-2">Sub Category</label>
          <input [(ngModel)]="keySubCategory"
                 [typeahead]="subCategories"
                 (typeaheadLoading)="loadSubCategories()"
                 (typeaheadOnSelect)="setSelectedSubCategory($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadOptionField="subCategoryName"
                 placeholder="Sub Category"
                 autocomplete="off"
                 class="form-control" name="subCategory">
        </div>
        <div class="col-12 col-sm-6 col-lg-2">
          <label class="form-label fw-semibold mb-2">Brand</label>
          <input [(ngModel)]="keyBrand"
                 [typeahead]="brands"
                 (typeaheadLoading)="loadBrands()"
                 (typeaheadOnSelect)="setSelectedBrand($event)"
                 [typeaheadOptionsLimit]="10"
                 typeaheadOptionField="name"
                 placeholder="Brand"
                 autocomplete="off"
                 class="form-control" name="brand">
        </div>
        <div class="col-12 col-lg-2">
          <div class="d-flex gap-2 flex-wrap">
            <button type="button" class="btn btn-primary flex-fill" (click)="openFilterModal()">
              <i class="fa fa-filter me-1"></i>Filters
            </button>
            <button type="button" class="btn btn-secondary flex-fill" (click)="findAllItems()">
              <i class="fa fa-refresh me-1"></i>Reset
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Active Filters Display -->
  <div class="row mt-2 mb-3" *ngIf="activeFilters && activeFilters.length > 0">
    <div class="col-12">
      <div class="card bg-light">
        <div class="card-body py-2 d-flex justify-content-between align-items-center flex-wrap">
          <div>
            <strong>Active Filters:</strong>
            <span class="badge bg-info text-white ms-2 me-1 mb-1" *ngFor="let filter of activeFilters">{{ filter }}</span>
          </div>
          <button class="btn btn-sm btn-primary-outline-secondary ms-1" (click)="clearFilters()" >
            <i class="fa fa-times"></i> Clear Filters
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Table settings controls -->
  <div class="d-flex justify-content-between align-items-center mb-2">
    <div>
      <button type="button" class="btn btn-sm btn-primary-outline-secondary ms-1" (click)="toggleColumnSelector()" >
        <i class="fa fa-columns"></i> Customize Columns
      </button>
    </div>
    <app-page-size-selector [pageSize]="pageSize" (pageSizeChange)="onPageSizeChange($event)"></app-page-size-selector>
  </div>

  <!-- Column selector -->
  <div *ngIf="showColumnSelector" class="mb-3 p-3 border rounded bg-light">
    <app-column-selector [columns]="tableColumns" (columnsChange)="onColumnsChange($event)"></app-column-selector>
  </div>

  <div class="content-section p-0" (load)="findAllItems()">
    <div class="table-responsive" style="min-height: 400px;">
      <table class="table table-professional table-hover table-striped">
        <thead class="table-light">
          <tr class="text-center">
            <!-- Dynamic columns based on user selection -->
            <th scope="col" *ngFor="let column of visibleColumns" [ngClass]="column.class">
              {{ column.header }}
            </th>
            <th scope="col" *ngIf="isAdmin && quickEditMode">Quick Edit</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of items; let i = index"
              (click)="selectRecord(item,i)" (dblclick)="edit()"
              [class.active]="i === selectedRow"
              class="text-center">
            <!-- Dynamic columns based on user selection -->
            <ng-container *ngFor="let column of visibleColumns">
              <!-- Barcode column -->
              <td *ngIf="column.key === 'barcode'" [ngClass]="column.class">{{ item.barcode }}</td>

              <!-- Item Code column -->
              <td *ngIf="column.key === 'itemCode'" [ngClass]="column.class">{{ item.itemCode }}</td>

              <!-- Item Name column -->
              <td *ngIf="column.key === 'itemName'" [ngClass]="column.class">{{ item.itemName }}</td>

              <!-- Category column -->
              <td *ngIf="column.key === 'itemCategory'" [ngClass]="column.class">{{ item.itemCategory != undefined ? item.itemCategory.categoryName : 'N/A' }}</td>

              <!-- Brand column -->
              <td *ngIf="column.key === 'brand'" [ngClass]="column.class">{{ item.brand != undefined ? item.brand.name : 'N/A' }}</td>

              <!-- Sub Category column -->
              <td *ngIf="column.key === 'subCategory'" [ngClass]="column.class">{{ item.subCategory != undefined ? item.subCategory.subCategoryName : 'N/A' }}</td>

              <!-- Model column -->
              <td *ngIf="column.key === 'model'" [ngClass]="column.class">{{ item.model != undefined ? item.model.name : 'N/A' }}</td>

              <!-- Supplier column -->
              <td *ngIf="column.key === 'supplier'" [ngClass]="column.class">{{ item.supplier != undefined ? item.supplier.name : 'N/A' }}</td>

              <!-- Selling Price column -->
              <td *ngIf="column.key === 'sellingPrice'" [ngClass]="column.class">{{ item.sellingPrice }}</td>

              <!-- Item Cost column -->
              <td *ngIf="column.key === 'itemCost'" [ngClass]="column.class">{{ item.itemCost }}</td>

              <!-- Reorder Level column -->
              <td *ngIf="column.key === 'reorderLevel'" [ngClass]="column.class">{{ item.deadStockLevel }}</td>



              <!-- Manage Stock column -->
              <td *ngIf="column.key === 'manageStock'" [ngClass]="column.class">
                <i class="fa" [ngClass]="{'fa-check text-success': item.manageStock, 'fa-times text-danger': !item.manageStock}"></i>
              </td>

              <!-- Has Empty Bottle column -->
              <td *ngIf="column.key === 'hasEmpty'" [ngClass]="column.class">
                <i class="fa" [ngClass]="{'fa-check text-success': item.hasEmpty, 'fa-times text-danger': !item.hasEmpty}"></i>
              </td>

              <!-- Empty Bottle column -->
              <td *ngIf="column.key === 'emptyBottle'" [ngClass]="column.class">
                {{ item.emptyBottle ? item.emptyBottle.itemName : 'N/A' }}
              </td>

              <!-- Active column -->
              <td *ngIf="column.key === 'active'" [ngClass]="column.class">
                <i class="fa" [ngClass]="{'fa-check text-success': item.active, 'fa-times text-danger': !item.active}"></i>
              </td>
            </ng-container>
            <td *ngIf="isAdmin && quickEditMode" (click)="$event.stopPropagation()" class="quick-edit-cell">
              <div class="d-flex flex-column align-items-start">

                <div class="d-flex mb-2 align-items-center">
                  <div class="form-check form-check-inline me-3">
                    <input class="form-check-input" type="checkbox" [id]="'manageStock-'+i" [(ngModel)]="item.manageStock" name="manageStock-{{i}}">
                    <label class="form-check-label" [for]="'manageStock-'+i">Manage Stock</label>
                  </div>
                  <div class="form-check form-check-inline">
                    <input class="form-check-input" type="checkbox" [id]="'active-'+i" [(ngModel)]="item.active" name="active-{{i}}">
                    <label class="form-check-label" [for]="'active-'+i">Active</label>
                  </div>
                  <button class="btn btn-sm btn-icon ml-auto"
                    [ngClass]="{
                      'btn-success': !item['saving'] && !item['saveSuccess'] && !item['saveError'],
                      'btn-info': item['saving'],
                      'btn-success save-success': item['saveSuccess'],
                      'btn-danger': item['saveError']
                    }"
                    (click)="saveQuickEdit(item)"
                    [disabled]="item['saving']" >
                    <i class="fa"
                      [ngClass]="{
                        'fa-save': !item['saving'] && !item['saveSuccess'] && !item['saveError'],
                        'fa-spinner fa-spin': item['saving'],
                        'fa-check': item['saveSuccess'],
                        'fa-times': item['saveError']
                      }"></i>
                  </button>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Mobile view for selected item details -->
    <div class="d-md-none mt-3 mb-3" *ngIf="selectedItem && selectedItem.id">
      <div class="card bg-light">
        <div class="card-body professional-card-body">
          <h5 class="card-title professional-card-title">Selected Item Details</h5>
          <div class="row g-2">
            <div class="col-6">
              <p class="mb-1 fw-bold">Category:</p>
              <p>{{ selectedItem.itemCategory != undefined ? selectedItem.itemCategory.categoryName : 'N/A' }}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Brand:</p>
              <p>{{ selectedItem.brand != undefined ? selectedItem.brand.name : 'N/A' }}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Item Cost:</p>
              <p>{{ selectedItem.itemCost != undefined ? selectedItem.itemCost : 'N/A' }}</p>
            </div>
            <div class="col-6" *ngIf="selectedItem.hasEmpty">
              <p class="mb-1 fw-bold">Has Empty Bottle:</p>
              <p><i class="fa fa-check text-success"></i> Yes</p>
            </div>
            <div class="col-12" *ngIf="selectedItem.hasEmpty && selectedItem.emptyBottle">
              <p class="mb-1 fw-bold">Empty Bottle:</p>
              <p>{{ selectedItem.emptyBottle.itemName }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Pagination -->
    <div class="row mt-3">
      <div class="col-12">
        <pagination class="pagination pagination pagination-sm justify-content-center"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    [maxSize]="5" [itemsPerPage]="pageSize"
                    [boundaryLinks]="true"
                    (pageChanged)="pageChanged($event)">
        </pagination>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="row mt-3 mb-3">
      <div class="col-12 text-end">
        <button type="button" class="btn btn-primary me-2" (click)="viewStock()" >
          <i class="fas fa-boxes me-2"></i>View Stock
        </button>
        <!--<button type="button" class="btn btn-danger me-2" (click)="openModalBarcode()" >Barcode</button>-->
        <button *ngIf="isAdmin" type="button" class="btn btn-warning me-2" (click)="openEditBarcodeModal()" >
          <i class="fas fa-edit me-2"></i>Edit Barcode
        </button>
        <button *ngIf="isAdmin" type="button" class="btn btn-primary me-2" (click)="toggleQuickEditMode()" >
          <i class="fas fa-bolt"></i> {{ quickEditMode ? 'Exit Quick Edit' : 'Quick Edit' }}
        </button>
        <button type="button" class="btn btn-danger" (click)="edit()" >Edit</button>
      </div>
    </div>
  </div>
  <!-- Bootstrap loading spinner -->
  <div *ngIf="loading" class="position-fixed w-100 h-100 d-flex justify-content-center align-items-center"
       style="top: 0; left: 0; background-color: rgba(0,0,0,0.3); z-index: 1050;">
    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>
</div>


